{"name": "quant-platform-tests", "version": "1.0.0", "description": "量化投资平台端到端测试", "main": "comprehensive_platform_test.js", "scripts": {"test": "node comprehensive_platform_test.js", "test:headless": "HEADLESS=true node comprehensive_platform_test.js", "test:debug": "DEBUG=true node comprehensive_platform_test.js"}, "dependencies": {"puppeteer": "^24.15.0"}, "keywords": ["puppeteer", "e2e", "testing", "quant", "platform"], "author": "Quant Platform Team", "license": "MIT"}