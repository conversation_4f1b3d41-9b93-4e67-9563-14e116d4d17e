const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

/**
 * 量化投资平台深度用户体验测试
 */

class QuantPlatformTester {
    constructor() {
        this.browser = null;
        this.page = null;
        this.testResults = [];
        this.screenshots = [];
        this.errors = [];
        this.baseUrl = 'http://localhost:5174';
        this.apiUrl = 'http://localhost:8000';
        
        this.config = {
            headless: false,
            slowMo: 100,
            timeout: 30000,
            viewport: { width: 1920, height: 1080 }
        };
    }

    async init() {
        console.log('🚀 启动量化投资平台深度测试...');
        
        this.browser = await puppeteer.launch({
            headless: this.config.headless,
            slowMo: this.config.slowMo,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        this.page = await this.browser.newPage();
        await this.page.setViewport(this.config.viewport);
        
        // 监听错误
        this.page.on('error', (error) => {
            this.logError('页面错误', error);
        });
        
        this.page.on('pageerror', (error) => {
            this.logError('页面脚本错误', error);
        });
        
        console.log('✅ 浏览器初始化完成');
    }

    async takeScreenshot(name, description = '') {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `screenshot_${timestamp}_${name}.png`;
        const filepath = path.join(__dirname, 'screenshots', filename);
        
        const screenshotDir = path.join(__dirname, 'screenshots');
        if (!fs.existsSync(screenshotDir)) {
            fs.mkdirSync(screenshotDir, { recursive: true });
        }
        
        await this.page.screenshot({ path: filepath, fullPage: true });
        
        this.screenshots.push({
            name, description, filename, filepath, timestamp: new Date()
        });
        
        console.log(`📸 截图保存: ${filename} - ${description}`);
    }

    logError(type, error) {
        const errorInfo = {
            type,
            error: error.toString(),
            stack: error.stack || '',
            timestamp: new Date(),
            url: this.page.url()
        };
        
        this.errors.push(errorInfo);
        console.error(`❌ ${type}:`, error);
    }

    logTestResult(testName, success, details = '', duration = 0) {
        const result = {
            testName, success, details, duration,
            timestamp: new Date(), url: this.page.url()
        };
        
        this.testResults.push(result);
        
        const status = success ? '✅' : '❌';
        const time = duration > 0 ? ` (${duration}ms)` : '';
        console.log(`${status} ${testName}${time}: ${details}`);
    }

    async testHomePage() {
        console.log('\n🏠 测试首页加载...');
        const startTime = Date.now();
        
        try {
            await this.page.goto(this.baseUrl, { 
                waitUntil: 'networkidle2',
                timeout: this.config.timeout 
            });
            
            await this.takeScreenshot('homepage_loaded', '首页加载完成');
            
            // 检查页面基本元素
            const title = await this.page.title();
            const bodyExists = await this.page.$('body');
            const navExists = await this.page.$('nav, .navbar, .header, .nav');
            
            console.log(`📄 页面标题: ${title}`);
            console.log(`✅ 页面主体: ${bodyExists ? '存在' : '不存在'}`);
            console.log(`✅ 导航栏: ${navExists ? '存在' : '不存在'}`);
            
            const duration = Date.now() - startTime;
            const success = bodyExists && title.length > 0;
            this.logTestResult('首页加载', success, `标题: ${title}`, duration);
            
            return success;
            
        } catch (error) {
            this.logError('首页加载失败', error);
            await this.takeScreenshot('homepage_error', '首页加载错误');
            this.logTestResult('首页加载', false, error.message);
            return false;
        }
    }

    async testNavigation() {
        console.log('\n🧭 测试页面导航...');
        const startTime = Date.now();
        
        try {
            // 查找所有导航链接
            const navLinks = await this.page.$$eval('a[href], nav a, .nav a, .navbar a', 
                links => links.map(link => ({
                    text: link.textContent.trim(),
                    href: link.href,
                    visible: link.offsetParent !== null
                })).filter(link => link.text && link.visible)
            );
            
            console.log(`🔗 找到 ${navLinks.length} 个导航链接:`);
            navLinks.forEach(link => {
                console.log(`   - ${link.text}: ${link.href}`);
            });
            
            await this.takeScreenshot('navigation_test', '导航测试');
            
            const duration = Date.now() - startTime;
            const success = navLinks.length > 0;
            this.logTestResult('页面导航', success, `找到${navLinks.length}个链接`, duration);
            
            return success;
            
        } catch (error) {
            this.logError('导航测试失败', error);
            await this.takeScreenshot('navigation_error', '导航测试错误');
            this.logTestResult('页面导航', false, error.message);
            return false;
        }
    }

    async testAPIConnectivity() {
        console.log('\n📡 测试API连接...');
        const startTime = Date.now();
        
        try {
            // 测试后端API是否可访问
            const response = await this.page.evaluate(async (apiUrl) => {
                try {
                    const res = await fetch(`${apiUrl}/docs`);
                    return {
                        status: res.status,
                        ok: res.ok,
                        statusText: res.statusText
                    };
                } catch (error) {
                    return {
                        error: error.message
                    };
                }
            }, this.apiUrl);
            
            console.log(`🌐 API响应:`, response);
            
            const duration = Date.now() - startTime;
            const success = response.ok || response.status === 200;
            this.logTestResult('API连接', success, `状态: ${response.status || 'Error'}`, duration);
            
            return success;
            
        } catch (error) {
            this.logError('API连接测试失败', error);
            this.logTestResult('API连接', false, error.message);
            return false;
        }
    }

    async testResponsiveDesign() {
        console.log('\n📱 测试响应式设计...');
        const startTime = Date.now();
        
        try {
            const viewports = [
                { width: 1920, height: 1080, name: '桌面端' },
                { width: 768, height: 1024, name: '平板端' },
                { width: 375, height: 667, name: '手机端' }
            ];
            
            let responsiveTests = 0;
            for (const viewport of viewports) {
                await this.page.setViewport(viewport);
                await this.page.waitForTimeout(1000);
                await this.takeScreenshot(`responsive_${viewport.name}`, `${viewport.name}显示效果`);
                
                const navigation = await this.page.$('nav, .navbar, .header');
                if (navigation) {
                    responsiveTests++;
                    console.log(`✅ ${viewport.name}导航栏正常`);
                } else {
                    console.log(`❌ ${viewport.name}导航栏异常`);
                }
            }
            
            await this.page.setViewport(this.config.viewport);
            
            const duration = Date.now() - startTime;
            const success = responsiveTests >= 2;
            this.logTestResult('响应式设计', success, `${responsiveTests}个视口测试通过`, duration);
            
            return success;
            
        } catch (error) {
            this.logError('响应式设计测试失败', error);
            await this.takeScreenshot('responsive_error', '响应式设计错误');
            this.logTestResult('响应式设计', false, error.message);
            return false;
        }
    }

    async runAllTests() {
        console.log('\n🚀 开始运行完整的平台测试套件...');
        const overallStartTime = Date.now();
        
        const tests = [
            { name: '首页加载', method: this.testHomePage },
            { name: '页面导航', method: this.testNavigation },
            { name: 'API连接', method: this.testAPIConnectivity },
            { name: '响应式设计', method: this.testResponsiveDesign }
        ];
        
        let passedTests = 0;
        
        for (const test of tests) {
            try {
                console.log(`\n${'='.repeat(50)}`);
                console.log(`🧪 执行测试: ${test.name}`);
                console.log(`${'='.repeat(50)}`);
                
                const result = await test.method.call(this);
                if (result) passedTests++;
                
                await this.page.waitForTimeout(2000);
                
            } catch (error) {
                this.logError(`测试执行失败: ${test.name}`, error);
            }
        }
        
        const overallDuration = Date.now() - overallStartTime;
        
        console.log(`\n${'='.repeat(60)}`);
        console.log(`📊 测试完成总结`);
        console.log(`${'='.repeat(60)}`);
        console.log(`✅ 通过测试: ${passedTests}/${tests.length}`);
        console.log(`⏱️  总耗时: ${overallDuration}ms`);
        console.log(`📸 截图数量: ${this.screenshots.length}`);
        console.log(`❌ 错误数量: ${this.errors.length}`);
        
        await this.generateReport();
        
        return {
            passed: passedTests,
            total: tests.length,
            duration: overallDuration,
            success: passedTests === tests.length
        };
    }

    async generateReport() {
        console.log('\n📝 生成测试报告...');
        
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                totalTests: this.testResults.length,
                passedTests: this.testResults.filter(r => r.success).length,
                failedTests: this.testResults.filter(r => !r.success).length,
                totalDuration: this.testResults.reduce((sum, r) => sum + r.duration, 0),
                screenshotCount: this.screenshots.length,
                errorCount: this.errors.length
            },
            testResults: this.testResults,
            screenshots: this.screenshots,
            errors: this.errors
        };
        
        const reportPath = path.join(__dirname, `test_report_${new Date().toISOString().replace(/[:.]/g, '-')}.json`);
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        console.log(`📄 测试报告: ${reportPath}`);
        return report;
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
            console.log('🧹 浏览器已关闭');
        }
    }
}

// 主执行函数
async function main() {
    const tester = new QuantPlatformTester();
    
    try {
        await tester.init();
        const results = await tester.runAllTests();
        
        console.log('\n🎉 测试执行完成!');
        console.log(`结果: ${results.passed}/${results.total} 测试通过`);
        
        if (results.success) {
            console.log('🎊 所有测试都通过了！');
        } else {
            console.log('⚠️  部分测试失败，请查看报告了解详情');
        }
        
    } catch (error) {
        console.error('💥 测试执行出现严重错误:', error);
    } finally {
        await tester.cleanup();
    }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
    main();
}

module.exports = QuantPlatformTester;
