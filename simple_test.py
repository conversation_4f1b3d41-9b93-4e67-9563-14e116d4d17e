#!/usr/bin/env python3
"""
简单功能检查脚本 - 测试所有API和功能
使用标准库进行测试
"""

import urllib.request
import urllib.parse
import json
import time
from typing import Dict, List, Any

class SimpleConsumerTester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.test_results = []
        self.auth_token = None
        
    def make_request(self, method: str, endpoint: str, data: Dict = None, headers: Dict = None) -> Dict:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        result = {
            "endpoint": endpoint,
            "method": method,
            "url": url,
            "success": False,
            "status_code": None,
            "response_time": 0,
            "error": None,
            "response_data": None
        }
        
        try:
            start_time = time.time()
            
            # 准备请求数据
            if data:
                data = json.dumps(data).encode('utf-8')
            
            # 准备请求头
            req_headers = {'Content-Type': 'application/json'}
            if headers:
                req_headers.update(headers)
            
            # 创建请求
            req = urllib.request.Request(url, data=data, headers=req_headers, method=method)
            
            # 发送请求
            with urllib.request.urlopen(req) as response:
                result["status_code"] = response.getcode()
                response_text = response.read().decode('utf-8')
                if response_text:
                    result["response_data"] = json.loads(response_text)
                
                result["response_time"] = time.time() - start_time
                result["success"] = 200 <= result["status_code"] < 300
                
        except urllib.error.HTTPError as e:
            result["status_code"] = e.code
            result["error"] = f"HTTP Error {e.code}: {e.reason}"
            result["response_time"] = time.time() - start_time
            try:
                error_response = e.read().decode('utf-8')
                result["response_data"] = json.loads(error_response)
            except:
                pass
                
        except Exception as e:
            result["error"] = str(e)
            result["response_time"] = time.time() - start_time
            
        return result
    
    def test_basic_endpoints(self):
        """测试基础端点"""
        print("🔍 测试基础端点...")
        
        # 测试健康检查
        result = self.make_request("GET", "/health")
        self.test_results.append(result)
        print(f"  健康检查: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
        
        # 测试API文档
        result = self.make_request("GET", "/docs")
        self.test_results.append(result)
        print(f"  API文档: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
        
        # 测试根路径
        result = self.make_request("GET", "/")
        self.test_results.append(result)
        print(f"  根路径: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
    
    def test_market_endpoints(self):
        """测试市场数据端点"""
        print("📈 测试市场数据端点...")
        
        # 测试股票列表
        result = self.make_request("GET", "/api/v1/market/stocks")
        self.test_results.append(result)
        print(f"  股票列表: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
        
        # 测试实时行情
        result = self.make_request("GET", "/api/v1/market/quotes/000001")
        self.test_results.append(result)
        print(f"  实时行情: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
        
        # 测试K线数据
        result = self.make_request("GET", "/api/v1/market/kline/000001?period=1d&limit=100")
        self.test_results.append(result)
        print(f"  K线数据: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
        
        # 测试市场深度
        result = self.make_request("GET", "/api/v1/market/depth/000001")
        self.test_results.append(result)
        print(f"  市场深度: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
    
    def test_trading_endpoints(self):
        """测试交易端点"""
        print("💰 测试交易端点...")
        
        # 测试交易概览
        result = self.make_request("GET", "/api/v1/trading/overview")
        self.test_results.append(result)
        print(f"  交易概览: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
        
        # 测试持仓查询
        result = self.make_request("GET", "/api/v1/trading/positions")
        self.test_results.append(result)
        print(f"  持仓查询: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
        
        # 测试快速下单接口（不实际下单）
        result = self.make_request("POST", "/api/v1/trading/quick-order", {
            "symbol": "000001",
            "side": "buy",
            "order_type": "limit",
            "quantity": 100,
            "price": 15.50
        })
        self.test_results.append(result)
        print(f"  快速下单接口: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
    
    def test_order_endpoints(self):
        """测试订单管理端点"""
        print("📋 测试订单管理端点...")
        
        # 测试订单列表
        result = self.make_request("GET", "/api/v1/orders?page=1&size=20")
        self.test_results.append(result)
        print(f"  订单列表: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
        
        # 测试订单详情
        result = self.make_request("GET", "/api/v1/orders/1")
        self.test_results.append(result)
        print(f"  订单详情: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
    
    def test_strategy_endpoints(self):
        """测试策略开发端点"""
        print("🧠 测试策略开发端点...")
        
        # 测试策略列表
        result = self.make_request("GET", "/api/v1/strategies")
        self.test_results.append(result)
        print(f"  策略列表: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
        
        # 测试策略模板
        result = self.make_request("GET", "/api/v1/strategies/templates")
        self.test_results.append(result)
        print(f"  策略模板: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
    
    def test_historical_endpoints(self):
        """测试历史数据端点"""
        print("📚 测试历史数据端点...")
        
        # 测试历史股票列表
        result = self.make_request("GET", "/api/v1/historical/stocks")
        self.test_results.append(result)
        print(f"  历史股票列表: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
        
        # 测试历史数据查询
        result = self.make_request("GET", "/api/v1/historical/data/000001?start_date=2024-01-01&end_date=2024-12-31")
        self.test_results.append(result)
        print(f"  历史数据查询: {'✅' if result['success'] else '❌'} (状态码: {result['status_code']})")
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "="*60)
        print("📊 功能测试报告")
        print("="*60)
        
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - successful_tests
        
        success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
        
        print(f"总测试数: {total_tests}")
        print(f"成功: {successful_tests} ✅")
        print(f"失败: {failed_tests} ❌")
        print(f"成功率: {success_rate:.1f}%")
        
        print("\n" + "-"*60)
        print("详细结果:")
        print("-"*60)
        
        for result in self.test_results:
            status_icon = "✅" if result['success'] else "❌"
            print(f"{status_icon} {result['method']} {result['endpoint']} - {result['status_code']}")
            if result['error']:
                print(f"    错误: {result['error']}")
            if result['response_time'] > 1:
                print(f"    响应时间: {result['response_time']:.2f}s (较慢)")
        
        print("\n" + "="*60)
        
        # 问题分析
        print("🔍 问题分析:")
        print("="*60)
        
        error_405_count = sum(1 for result in self.test_results if result['status_code'] == 405)
        error_404_count = sum(1 for result in self.test_results if result['status_code'] == 404)
        error_500_count = sum(1 for result in self.test_results if result['status_code'] == 500)
        auth_errors = sum(1 for result in self.test_results if result['status_code'] == 401)
        
        if error_405_count > 0:
            print(f"❌ 发现 {error_405_count} 个 405 错误 (方法不允许)")
        if error_404_count > 0:
            print(f"❌ 发现 {error_404_count} 个 404 错误 (路径不存在)")
        if error_500_count > 0:
            print(f"❌ 发现 {error_500_count} 个 500 错误 (服务器内部错误)")
        if auth_errors > 0:
            print(f"❌ 发现 {auth_errors} 个 401 错误 (认证失败)")
        
        if failed_tests == 0:
            print("🎉 所有功能正常运行!")
        else:
            print(f"⚠️  发现 {failed_tests} 个问题需要修复")
        
        # 返回问题列表
        return [result for result in self.test_results if not result['success']]
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始全面功能检查...")
        print("="*60)
        
        self.test_basic_endpoints()
        self.test_market_endpoints()
        self.test_trading_endpoints()
        self.test_order_endpoints()
        self.test_strategy_endpoints()
        self.test_historical_endpoints()
        
        failed_tests = self.generate_report()
        return failed_tests

def main():
    """主函数"""
    tester = SimpleConsumerTester()
    failed_tests = tester.run_all_tests()
    
    if failed_tests:
        print("\n" + "🛠️  需要修复的问题:")
        print("="*60)
        for i, test in enumerate(failed_tests, 1):
            print(f"{i}. {test['method']} {test['endpoint']}")
            print(f"   状态码: {test['status_code']}")
            print(f"   错误: {test['error']}")
            print()

if __name__ == "__main__":
    main()