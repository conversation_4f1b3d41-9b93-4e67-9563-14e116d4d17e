#!/usr/bin/env python3
"""
测试登录流程
验证前端登录页面和后端API的连接
"""

import requests
import json
import time

def test_login_api():
    """测试登录API"""
    print("🔍 测试登录API...")
    
    # 测试登录端点
    login_url = "http://localhost:8000/api/v1/auth/login"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(login_url, json=login_data, timeout=10)
        print(f"登录API状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 登录API正常工作")
            print(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 登录API失败: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 登录API连接失败: {e}")
        return False

def test_frontend_access():
    """测试前端页面访问"""
    print("\n🔍 测试前端页面访问...")
    
    try:
        # 测试前端登录页面
        frontend_url = "http://localhost:5176/login"
        response = requests.get(frontend_url, timeout=10)
        
        if response.status_code == 200:
            print("✅ 前端登录页面可访问")
            return True
        else:
            print(f"❌ 前端登录页面访问失败: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 前端页面连接失败: {e}")
        return False

def test_cors_configuration():
    """测试CORS配置"""
    print("\n🔍 测试CORS配置...")
    
    try:
        # 发送OPTIONS请求测试CORS
        login_url = "http://localhost:8000/api/v1/auth/login"
        headers = {
            'Origin': 'http://localhost:5176',
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type'
        }
        
        response = requests.options(login_url, headers=headers, timeout=10)
        print(f"CORS预检请求状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ CORS配置正常")
            cors_headers = {k: v for k, v in response.headers.items() if 'access-control' in k.lower()}
            print(f"CORS头信息: {json.dumps(cors_headers, indent=2)}")
            return True
        else:
            print(f"❌ CORS配置可能有问题: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ CORS测试连接失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试登录流程...")
    print("=" * 50)
    
    # 测试结果
    results = []
    
    # 1. 测试后端API
    results.append(test_login_api())
    
    # 2. 测试前端访问
    results.append(test_frontend_access())
    
    # 3. 测试CORS配置
    results.append(test_cors_configuration())
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    test_names = ["后端登录API", "前端页面访问", "CORS配置"]
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！登录流程应该正常工作。")
    else:
        print("⚠️  部分测试失败，请检查相关配置。")

if __name__ == "__main__":
    main()
