import request from '@/utils/request'

/**
 * 模拟交易API接口
 */

// 创建模拟账户
export function createSimulatedAccount(data) {
  return request({
    url: '/api/v1/simulated/accounts/create',
    method: 'post',
    data
  })
}

// 获取模拟账户列表
export function getSimulatedAccounts() {
  return request({
    url: '/api/v1/simulated/accounts',
    method: 'get'
  })
}

// 切换账户
export function switchAccountType(accountType) {
  return request({
    url: `/api/v1/simulated/accounts/switch/${accountType}`,
    method: 'post'
  })
}

// 重置模拟账户
export function resetSimulatedAccount(accountId) {
  return request({
    url: `/api/v1/simulated/accounts/${accountId}/reset`,
    method: 'post'
  })
}

// 创建模拟订单
export function createSimulatedOrder(data) {
  return request({
    url: '/api/v1/simulated/orders',
    method: 'post',
    data
  })
}

// 获取当前账户
export function getCurrentAccount() {
  return request({
    url: '/api/v1/simulated/current-account',
    method: 'get'
  })
}

// 获取账户业绩
export function getAccountPerformance(accountId, period = '1M') {
  return request({
    url: `/api/v1/simulated/performance/${accountId}`,
    method: 'get',
    params: { period }
  })
}

// 获取模拟交易指南
export function getSimulatedGuide() {
  return request({
    url: '/api/v1/simulated/guide',
    method: 'get'
  })
}

// 更新账户设置
export function updateAccountSettings(accountId, settings) {
  return request({
    url: `/api/v1/simulated/accounts/${accountId}/settings`,
    method: 'put',
    data: settings
  })
}