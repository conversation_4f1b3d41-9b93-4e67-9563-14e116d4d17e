<template>
  <div class="account-switcher">
    <el-dropdown @command="handleSwitch" trigger="click">
      <span class="account-info">
        <el-badge 
          :value="accountTypeBadge" 
          :type="badgeType"
          class="account-badge"
        >
          <div class="account-display">
            <i :class="accountIcon"></i>
            <span class="account-name">{{ currentAccountName }}</span>
            <i class="el-icon-arrow-down el-icon--right"></i>
          </div>
        </el-badge>
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item 
          command="REAL" 
          :disabled="!hasRealAccount"
        >
          <i class="el-icon-coin"></i>
          <span>真实账户</span>
          <span class="account-hint" v-if="realAccount">
            可用: ¥{{ formatMoney(realAccount.available_cash) }}
          </span>
        </el-dropdown-item>
        
        <el-dropdown-item command="SIMULATED">
          <i class="el-icon-school"></i>
          <span>模拟账户</span>
          <span class="account-hint" v-if="simulatedAccount">
            可用: ¥{{ formatMoney(simulatedAccount.available_cash) }}
          </span>
        </el-dropdown-item>
        
        <el-dropdown-item divided command="CREATE_SIMULATED">
          <i class="el-icon-plus"></i>
          <span>创建新模拟账户</span>
        </el-dropdown-item>
        
        <el-dropdown-item 
          command="RESET_SIMULATED" 
          v-if="currentAccount && currentAccount.account_type === 'SIMULATED'"
        >
          <i class="el-icon-refresh"></i>
          <span>重置模拟账户</span>
        </el-dropdown-item>
        
        <el-dropdown-item divided command="GUIDE">
          <i class="el-icon-question"></i>
          <span>模拟交易指南</span>
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    
    <!-- 模拟交易提示 -->
    <transition name="fade">
      <div 
        v-if="showSimulatedTip && currentAccount && currentAccount.account_type === 'SIMULATED'"
        class="simulated-tip"
      >
        <i class="el-icon-info"></i>
        当前为模拟交易模式，使用虚拟资金交易
        <i class="el-icon-close" @click="showSimulatedTip = false"></i>
      </div>
    </transition>
    
    <!-- 创建模拟账户对话框 -->
    <el-dialog
      title="创建模拟账户"
      :visible.sync="showCreateDialog"
      width="500px"
    >
      <el-form :model="createForm" label-width="120px">
        <el-form-item label="初始资金">
          <el-input-number
            v-model="createForm.initial_capital"
            :min="10000"
            :max="*********"
            :step="10000"
            style="width: 100%"
          ></el-input-number>
        </el-form-item>
        
        <el-form-item label="创建原因">
          <el-radio-group v-model="createForm.reason">
            <el-radio label="USER_CREATE">个人练习</el-radio>
            <el-radio label="STRATEGY_TEST">策略测试</el-radio>
            <el-radio label="EDUCATION">学习教育</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="高级设置">
          <el-checkbox v-model="showAdvancedSettings">
            显示高级设置
          </el-checkbox>
        </el-form-item>
        
        <template v-if="showAdvancedSettings">
          <el-form-item label="滑点模式">
            <el-select v-model="createForm.settings.slippage_mode">
              <el-option label="固定滑点" value="FIXED"></el-option>
              <el-option label="随机滑点" value="RANDOM"></el-option>
              <el-option label="市场冲击" value="MARKET_IMPACT"></el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="执行延迟">
            <el-input-number
              v-model="createForm.settings.execution_delay"
              :min="0"
              :max="5000"
              :step="100"
            >
            </el-input-number>
            <span class="input-hint">毫秒</span>
          </el-form-item>
          
          <el-form-item label="部分成交">
            <el-switch v-model="createForm.settings.partial_fill"></el-switch>
          </el-form-item>
        </template>
      </el-form>
      
      <span slot="footer">
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleCreateAccount"
          :loading="creating"
        >
          创建
        </el-button>
      </span>
    </el-dialog>
    
    <!-- 模拟交易指南对话框 -->
    <el-dialog
      title="模拟交易指南"
      :visible.sync="showGuideDialog"
      width="700px"
    >
      <div class="guide-content">
        <h3>{{ guide.introduction }}</h3>
        
        <h4>功能特性</h4>
        <ul>
          <li v-for="feature in guide.features" :key="feature">
            {{ feature }}
          </li>
        </ul>
        
        <h4>交易规则</h4>
        <div class="rules-section">
          <p><strong>交易时间：</strong>{{ guide.rules.trading_hours }}</p>
          <p><strong>涨跌停限制：</strong>{{ guide.rules.price_limit }}</p>
          <p><strong>最小交易单位：</strong>{{ guide.rules.min_volume }}</p>
          
          <h5>交易费用</h5>
          <table class="fee-table">
            <tr>
              <td>券商佣金</td>
              <td>{{ guide.rules.commission.broker }}</td>
            </tr>
            <tr>
              <td>印花税</td>
              <td>{{ guide.rules.commission.stamp_duty }}</td>
            </tr>
            <tr>
              <td>过户费</td>
              <td>{{ guide.rules.commission.transfer_fee }}</td>
            </tr>
            <tr>
              <td>证管费</td>
              <td>{{ guide.rules.commission.regulatory_fee }}</td>
            </tr>
          </table>
        </div>
        
        <h4>使用提示</h4>
        <ul>
          <li v-for="tip in guide.tips" :key="tip">
            {{ tip }}
          </li>
        </ul>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import { getSimulatedGuide } from '@/api/simulated'

export default {
  name: 'AccountSwitcher',
  
  data() {
    return {
      showSimulatedTip: true,
      showCreateDialog: false,
      showGuideDialog: false,
      showAdvancedSettings: false,
      creating: false,
      createForm: {
        initial_capital: 1000000,
        reason: 'USER_CREATE',
        settings: {
          slippage_mode: 'RANDOM',
          execution_delay: 300,
          partial_fill: true
        }
      },
      guide: {
        introduction: '',
        features: [],
        rules: {
          trading_hours: '',
          price_limit: '',
          min_volume: '',
          commission: {}
        },
        tips: []
      }
    }
  },
  
  computed: {
    ...mapState('account', [
      'currentAccount',
      'realAccount',
      'simulatedAccount'
    ]),
    
    hasRealAccount() {
      return !!this.realAccount
    },
    
    currentAccountName() {
      if (!this.currentAccount) return '未登录'
      return this.currentAccount.account_type === 'SIMULATED' 
        ? '模拟账户' 
        : '真实账户'
    },
    
    accountTypeBadge() {
      if (!this.currentAccount) return ''
      return this.currentAccount.account_type === 'SIMULATED' ? '模拟' : '真实'
    },
    
    badgeType() {
      if (!this.currentAccount) return 'info'
      return this.currentAccount.account_type === 'SIMULATED' ? 'warning' : 'success'
    },
    
    accountIcon() {
      if (!this.currentAccount) return 'el-icon-user'
      return this.currentAccount.account_type === 'SIMULATED' 
        ? 'el-icon-school' 
        : 'el-icon-coin'
    }
  },
  
  mounted() {
    this.loadCurrentAccount()
  },
  
  methods: {
    ...mapActions('account', [
      'loadCurrentAccount',
      'switchAccount',
      'createSimulatedAccount',
      'resetSimulatedAccount'
    ]),
    
    formatMoney(value) {
      if (!value) return '0.00'
      return new Intl.NumberFormat('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(value)
    },
    
    async handleSwitch(command) {
      switch (command) {
        case 'REAL':
        case 'SIMULATED':
          await this.handleSwitchAccount(command)
          break
        case 'CREATE_SIMULATED':
          this.showCreateDialog = true
          break
        case 'RESET_SIMULATED':
          await this.handleResetAccount()
          break
        case 'GUIDE':
          await this.showGuide()
          break
      }
    },
    
    async handleSwitchAccount(accountType) {
      try {
        await this.switchAccount(accountType)
        this.$message.success(`已切换到${accountType === 'SIMULATED' ? '模拟' : '真实'}账户`)
        
        // 刷新页面数据
        this.$emit('account-switched', accountType)
        
        // 显示模拟交易提示
        if (accountType === 'SIMULATED') {
          this.showSimulatedTip = true
        }
      } catch (error) {
        this.$message.error('切换账户失败')
      }
    },
    
    async handleCreateAccount() {
      this.creating = true
      try {
        await this.createSimulatedAccount(this.createForm)
        this.$message.success('创建模拟账户成功')
        this.showCreateDialog = false
        
        // 切换到新创建的模拟账户
        await this.handleSwitchAccount('SIMULATED')
      } catch (error) {
        this.$message.error('创建模拟账户失败')
      } finally {
        this.creating = false
      }
    },
    
    async handleResetAccount() {
      try {
        await this.$confirm(
          '重置将清空所有交易记录和持仓，资金恢复到初始状态，是否继续？',
          '确认重置',
          {
            confirmButtonText: '确定重置',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        await this.resetSimulatedAccount(this.currentAccount.id)
        this.$message.success('模拟账户已重置')
        
        // 刷新页面数据
        this.$emit('account-reset')
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('重置失败')
        }
      }
    },
    
    async showGuide() {
      try {
        const response = await getSimulatedGuide()
        this.guide = response.data
        this.showGuideDialog = true
      } catch (error) {
        this.$message.error('获取指南失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.account-switcher {
  position: relative;
  
  .account-info {
    cursor: pointer;
    
    .account-badge {
      ::v-deep .el-badge__content {
        right: 10px;
        transform: translateY(-50%) translateX(100%);
      }
    }
    
    .account-display {
      display: flex;
      align-items: center;
      padding: 8px 16px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      background: #fff;
      transition: all 0.3s;
      
      &:hover {
        border-color: #409eff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      
      i:first-child {
        margin-right: 8px;
        font-size: 18px;
      }
      
      .account-name {
        margin-right: 8px;
        font-weight: 500;
      }
    }
  }
  
  .el-dropdown-menu__item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 250px;
    
    i {
      margin-right: 8px;
    }
    
    .account-hint {
      color: #909399;
      font-size: 12px;
      margin-left: auto;
    }
  }
  
  .simulated-tip {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    margin-top: 8px;
    padding: 8px 12px;
    background: #fff4e6;
    border: 1px solid #ffd666;
    border-radius: 4px;
    color: #d48806;
    font-size: 13px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 10;
    
    i:first-child {
      margin-right: 8px;
    }
    
    i:last-child {
      margin-left: auto;
      cursor: pointer;
      
      &:hover {
        color: #ad6800;
      }
    }
  }
  
  .input-hint {
    margin-left: 8px;
    color: #909399;
    font-size: 12px;
  }
  
  .guide-content {
    h3 {
      color: #303133;
      margin-bottom: 16px;
    }
    
    h4 {
      color: #606266;
      margin: 24px 0 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid #ebeef5;
    }
    
    h5 {
      color: #909399;
      margin: 16px 0 8px;
    }
    
    ul {
      padding-left: 20px;
      
      li {
        margin: 8px 0;
        color: #606266;
      }
    }
    
    .rules-section {
      p {
        margin: 8px 0;
        color: #606266;
      }
    }
    
    .fee-table {
      width: 100%;
      margin-top: 8px;
      border-collapse: collapse;
      
      td {
        padding: 8px;
        border: 1px solid #ebeef5;
        
        &:first-child {
          background: #f5f7fa;
          font-weight: 500;
          width: 120px;
        }
      }
    }
  }
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}
</style>