<template>
  <div class="trading-view">
    <!-- 顶部工具栏 -->
    <div class="trading-toolbar">
      <div class="toolbar-left">
        <account-switcher @account-switched="handleAccountSwitched" @account-reset="handleAccountReset" />
      </div>
      <div class="toolbar-right">
        <el-button-group>
          <el-button :type="viewMode === 'simple' ? 'primary' : ''" @click="viewMode = 'simple'">
            简洁模式
          </el-button>
          <el-button :type="viewMode === 'pro' ? 'primary' : ''" @click="viewMode = 'pro'">
            专业模式
          </el-button>
        </el-button-group>
      </div>
    </div>
    
    <!-- 模拟交易提示 -->
    <el-alert
      v-if="isSimulatedMode"
      :title="simulatedAlertTitle"
      type="warning"
      :closable="false"
      show-icon
      class="simulated-alert"
    >
      <div class="alert-content">
        <span>{{ simulatedAlertContent }}</span>
        <el-button size="mini" type="text" @click="showSimulatedInfo">
          <i class="el-icon-info"></i> 了解更多
        </el-button>
      </div>
    </el-alert>
    
    <!-- 主交易区域 -->
    <div class="trading-main" :class="{ 'simulated-mode': isSimulatedMode }">
      <el-row :gutter="20">
        <!-- 左侧：行情和持仓 -->
        <el-col :span="16">
          <!-- 行情展示 -->
          <div class="market-section">
            <market-quotes 
              :is-simulated="isSimulatedMode"
              @select-stock="handleSelectStock" 
            />
          </div>
          
          <!-- 持仓和订单标签页 -->
          <el-tabs v-model="activeTab" class="info-tabs">
            <el-tab-pane label="持仓" name="positions">
              <position-list 
                :is-simulated="isSimulatedMode"
                @refresh="loadPositions"
              />
            </el-tab-pane>
            <el-tab-pane label="当日委托" name="orders">
              <order-list 
                :is-simulated="isSimulatedMode"
                :orders="todayOrders"
                @refresh="loadOrders"
                @cancel="handleCancelOrder"
              />
            </el-tab-pane>
            <el-tab-pane label="当日成交" name="trades">
              <trade-list 
                :is-simulated="isSimulatedMode"
                :trades="todayTrades"
                @refresh="loadTrades"
              />
            </el-tab-pane>
          </el-tabs>
        </el-col>
        
        <!-- 右侧：交易面板 -->
        <el-col :span="8">
          <!-- 账户信息 -->
          <account-info 
            :account="currentAccount"
            :is-simulated="isSimulatedMode"
            class="account-info-card"
          />
          
          <!-- 交易面板 -->
          <trading-panel
            :selected-stock="selectedStock"
            :is-simulated="isSimulatedMode"
            :available-cash="availableCash"
            @order-submitted="handleOrderSubmitted"
            class="trading-panel-card"
          />
          
          <!-- 快速操作 -->
          <quick-actions 
            v-if="viewMode === 'pro'"
            :is-simulated="isSimulatedMode"
            class="quick-actions-card"
          />
        </el-col>
      </el-row>
    </div>
    
    <!-- 模拟交易信息对话框 -->
    <el-dialog
      title="模拟交易说明"
      :visible.sync="showSimulatedDialog"
      width="600px"
    >
      <div class="simulated-info">
        <h4>模拟交易特点</h4>
        <ul>
          <li>使用虚拟资金进行交易，不会影响真实资金</li>
          <li>基于真实市场行情进行模拟成交</li>
          <li>完整的交易规则和手续费计算</li>
          <li>独立的持仓和交易记录</li>
          <li>可以随时重置账户，重新开始</li>
        </ul>
        
        <h4>与真实交易的区别</h4>
        <table class="comparison-table">
          <tr>
            <th>项目</th>
            <th>模拟交易</th>
            <th>真实交易</th>
          </tr>
          <tr>
            <td>资金</td>
            <td>虚拟资金</td>
            <td>真实资金</td>
          </tr>
          <tr>
            <td>成交</td>
            <td>模拟成交（含滑点）</td>
            <td>真实成交</td>
          </tr>
          <tr>
            <td>手续费</td>
            <td>按真实标准计算</td>
            <td>实际扣除</td>
          </tr>
          <tr>
            <td>风险</td>
            <td>无资金风险</td>
            <td>真实盈亏</td>
          </tr>
        </table>
        
        <div class="tips">
          <i class="el-icon-info"></i>
          建议先在模拟环境中熟悉交易流程和验证策略，再进行真实交易
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex'
import AccountSwitcher from '@/components/SimulatedTrading/AccountSwitcher'
import MarketQuotes from '@/components/Trading/MarketQuotes'
import PositionList from '@/components/Trading/PositionList'
import OrderList from '@/components/Trading/OrderList'
import TradeList from '@/components/Trading/TradeList'
import AccountInfo from '@/components/Trading/AccountInfo'
import TradingPanel from '@/components/Trading/TradingPanel'
import QuickActions from '@/components/Trading/QuickActions'

export default {
  name: 'TradingView',
  
  components: {
    AccountSwitcher,
    MarketQuotes,
    PositionList,
    OrderList,
    TradeList,
    AccountInfo,
    TradingPanel,
    QuickActions
  },
  
  data() {
    return {
      viewMode: 'simple',
      activeTab: 'positions',
      selectedStock: null,
      todayOrders: [],
      todayTrades: [],
      showSimulatedDialog: false,
      loading: false,
      refreshTimer: null
    }
  },
  
  computed: {
    ...mapState('account', ['currentAccount', 'isSimulated']),
    ...mapGetters('account', ['availableCash']),
    
    isSimulatedMode() {
      return this.isSimulated
    },
    
    simulatedAlertTitle() {
      return '当前为模拟交易模式'
    },
    
    simulatedAlertContent() {
      return '使用虚拟资金进行交易，所有交易基于真实市场行情模拟成交。'
    }
  },
  
  mounted() {
    this.initializeData()
    this.startAutoRefresh()
  },
  
  beforeDestroy() {
    this.stopAutoRefresh()
  },
  
  methods: {
    ...mapActions('account', ['loadCurrentAccount']),
    
    async initializeData() {
      this.loading = true
      try {
        // 加载账户信息
        await this.loadCurrentAccount()
        
        // 加载交易数据
        await Promise.all([
          this.loadPositions(),
          this.loadOrders(),
          this.loadTrades()
        ])
      } catch (error) {
        console.error('初始化数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },
    
    async loadPositions() {
      // 加载持仓数据
      try {
        // 调用API获取持仓
        // const response = await getPositions()
        // this.positions = response.data
      } catch (error) {
        console.error('加载持仓失败:', error)
      }
    },
    
    async loadOrders() {
      // 加载当日委托
      try {
        // 调用API获取订单
        // const response = await getTodayOrders()
        // this.todayOrders = response.data
      } catch (error) {
        console.error('加载委托失败:', error)
      }
    },
    
    async loadTrades() {
      // 加载当日成交
      try {
        // 调用API获取成交
        // const response = await getTodayTrades()
        // this.todayTrades = response.data
      } catch (error) {
        console.error('加载成交失败:', error)
      }
    },
    
    handleAccountSwitched(accountType) {
      // 账户切换后重新加载数据
      this.initializeData()
      
      if (accountType === 'SIMULATED') {
        this.$notify({
          title: '已切换到模拟账户',
          message: '当前使用虚拟资金进行交易',
          type: 'warning',
          duration: 3000
        })
      } else {
        this.$notify({
          title: '已切换到真实账户',
          message: '请谨慎操作，注意风险控制',
          type: 'success',
          duration: 3000
        })
      }
    },
    
    handleAccountReset() {
      // 账户重置后重新加载数据
      this.initializeData()
      this.$message.success('账户已重置')
    },
    
    handleSelectStock(stock) {
      this.selectedStock = stock
    },
    
    async handleOrderSubmitted(order) {
      // 订单提交成功后刷新数据
      await this.loadOrders()
      this.$message.success('订单提交成功')
    },
    
    async handleCancelOrder(orderId) {
      try {
        // 调用API取消订单
        // await cancelOrder(orderId)
        this.$message.success('撤单成功')
        await this.loadOrders()
      } catch (error) {
        this.$message.error('撤单失败')
      }
    },
    
    showSimulatedInfo() {
      this.showSimulatedDialog = true
    },
    
    startAutoRefresh() {
      // 每5秒刷新一次数据
      this.refreshTimer = setInterval(() => {
        if (!this.loading) {
          this.loadOrders()
          this.loadTrades()
        }
      }, 5000)
    },
    
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.trading-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  
  .trading-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: #fff;
    border-bottom: 1px solid #e4e7ed;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  }
  
  .simulated-alert {
    margin: 0;
    border-radius: 0;
    border: none;
    border-bottom: 1px solid #ffd666;
    
    ::v-deep .el-alert__content {
      .alert-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        
        .el-button {
          margin-left: 16px;
        }
      }
    }
  }
  
  .trading-main {
    flex: 1;
    padding: 20px;
    overflow: auto;
    
    &.simulated-mode {
      // 模拟模式特殊样式
      .el-card {
        border-color: #faad14;
      }
    }
  }
  
  .market-section {
    margin-bottom: 20px;
  }
  
  .info-tabs {
    background: #fff;
    padding: 0 20px;
    border-radius: 4px;
    
    ::v-deep .el-tabs__header {
      margin: 0;
    }
  }
  
  .account-info-card,
  .trading-panel-card,
  .quick-actions-card {
    margin-bottom: 20px;
  }
  
  .simulated-info {
    h4 {
      color: #303133;
      margin: 20px 0 12px;
      
      &:first-child {
        margin-top: 0;
      }
    }
    
    ul {
      padding-left: 20px;
      margin: 0;
      
      li {
        margin: 8px 0;
        color: #606266;
      }
    }
    
    .comparison-table {
      width: 100%;
      border-collapse: collapse;
      margin: 16px 0;
      
      th, td {
        padding: 12px;
        text-align: left;
        border: 1px solid #ebeef5;
      }
      
      th {
        background: #f5f7fa;
        font-weight: 600;
        color: #303133;
      }
      
      tr:nth-child(even) td {
        background: #fafafa;
      }
    }
    
    .tips {
      margin-top: 24px;
      padding: 12px 16px;
      background: #fff7e6;
      border: 1px solid #ffd666;
      border-radius: 4px;
      color: #d48806;
      display: flex;
      align-items: center;
      
      i {
        margin-right: 8px;
        font-size: 16px;
      }
    }
  }
}
</style>