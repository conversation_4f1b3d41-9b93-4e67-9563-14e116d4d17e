/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AccountSwitcher: typeof import('./../components/SimulatedTrading/AccountSwitcher.vue')['default']
    AdvancedKLineChart: typeof import('./../components/charts/AdvancedKLineChart.vue')['default']
    AppButton: typeof import('./../components/common/AppButton/index.vue')['default']
    AppCard: typeof import('./../components/common/AppCard/index.vue')['default']
    AppModal: typeof import('./../components/common/AppModal/index.vue')['default']
    AssetTrendChart: typeof import('./../components/charts/AssetTrendChart.vue')['default']
    BacktestForm: typeof import('./../components/backtest/BacktestForm.vue')['default']
    BacktestReports: typeof import('./../components/strategy/BacktestReports.vue')['default']
    BacktestResults: typeof import('./../components/backtest/BacktestResults.vue')['default']
    CTPTradingPanel: typeof import('./../components/trading/CTPTradingPanel.vue')['default']
    DepthChart: typeof import('./../components/charts/DepthChart/index.vue')['default']
    DrawdownChart: typeof import('./../components/charts/DrawdownChart.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAutocomplete: typeof import('element-plus/es')['ElAutocomplete']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCheckTag: typeof import('element-plus/es')['ElCheckTag']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRate: typeof import('element-plus/es')['ElRate']
    ElResult: typeof import('element-plus/es')['ElResult']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElStatistic: typeof import('element-plus/es')['ElStatistic']
    ElStep: typeof import('element-plus/es')['ElStep']
    ElSteps: typeof import('element-plus/es')['ElSteps']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    EquityCurveChart: typeof import('./../components/charts/EquityCurveChart.vue')['default']
    ErrorBoundary: typeof import('./../components/common/ErrorBoundary.vue')['default']
    KLineChart: typeof import('./../components/charts/KLineChart/index.vue')['default']
    MetricCard: typeof import('./../components/widgets/MetricCard.vue')['default']
    OrderBook: typeof import('./../components/trading/OrderBook.vue')['default']
    OrderDetailPanel: typeof import('./../components/trading/OrderDetailPanel.vue')['default']
    OrderForm: typeof import('./../components/trading/OrderForm/index.vue')['default']
    OrderManagement: typeof import('./../components/trading/OrderManagement.vue')['default']
    OrderModifyForm: typeof import('./../components/trading/OrderModifyForm.vue')['default']
    ParameterOptimizer: typeof import('./../components/strategy/ParameterOptimizer.vue')['default']
    PositionList: typeof import('./../components/trading/PositionList.vue')['default']
    PositionManagement: typeof import('./../components/trading/PositionManagement.vue')['default']
    PositionPieChart: typeof import('./../components/charts/PositionPieChart.vue')['default']
    QuickOrderForm: typeof import('./../components/trading/QuickOrderForm.vue')['default']
    RealTimeOrderList: typeof import('./../components/trading/RealTimeOrderList.vue')['default']
    RealTimePositionList: typeof import('./../components/trading/RealTimePositionList.vue')['default']
    RealTimeRiskMonitor: typeof import('./../components/risk/RealTimeRiskMonitor.vue')['default']
    RiskAlertSystem: typeof import('./../components/risk/RiskAlertSystem.vue')['default']
    RiskDistributionChart: typeof import('./../components/charts/RiskDistributionChart.vue')['default']
    RiskTrendChart: typeof import('./../components/charts/RiskTrendChart.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SkeletonLoader: typeof import('./../components/common/SkeletonLoader.vue')['default']
    SliderCaptcha: typeof import('./../components/common/SliderCaptcha/index.vue')['default']
    SortIcon: typeof import('./../components/market/SortIcon.vue')['default']
    StockCard: typeof import('./../components/market/StockCard.vue')['default']
    StockSelector: typeof import('./../components/trading/StockSelector.vue')['default']
    StrategyCard: typeof import('./../components/strategy/StrategyCard/index.vue')['default']
    StrategyCreateWizard: typeof import('./../components/strategy/StrategyCreateWizard.vue')['default']
    StrategyEditor: typeof import('./../components/strategy/StrategyEditor.vue')['default']
    StrategyLibrary: typeof import('./../components/strategy/StrategyLibrary.vue')['default']
    StrategyMarket: typeof import('./../components/strategy/StrategyMarket.vue')['default']
    StrategySearch: typeof import('./../components/strategy/StrategySearch.vue')['default']
    TradingAnalysisChart: typeof import('./../components/charts/TradingAnalysisChart.vue')['default']
    TradingDataGrid: typeof import('./../components/trading/TradingDataGrid/index.vue')['default']
    TradingTerminal: typeof import('./../components/trading/TradingTerminal.vue')['default']
    UserGuide: typeof import('./../components/guide/UserGuide.vue')['default']
    VirtualList: typeof import('./../components/common/VirtualList.vue')['default']
    VirtualTable: typeof import('./../components/common/VirtualTable/index.vue')['default']
  }
  export interface GlobalDirectives {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
