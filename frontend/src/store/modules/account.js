import { 
  getCurrentAccount, 
  switchAccountType,
  createSimulatedAccount as createAccount,
  resetSimulatedAccount as resetAccount
} from '@/api/simulated'

const state = {
  currentAccount: null,
  realAccount: null,
  simulatedAccount: null,
  isSimulated: false,
  loading: false
}

const mutations = {
  SET_CURRENT_ACCOUNT(state, account) {
    state.currentAccount = account
    state.isSimulated = account && account.account_type === 'SIMULATED'
    
    // 更新对应类型的账户
    if (account) {
      if (account.account_type === 'SIMULATED') {
        state.simulatedAccount = account
      } else {
        state.realAccount = account
      }
    }
  },
  
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  
  CLEAR_ACCOUNTS(state) {
    state.currentAccount = null
    state.realAccount = null
    state.simulatedAccount = null
    state.isSimulated = false
  }
}

const actions = {
  // 加载当前账户
  async loadCurrentAccount({ commit }) {
    commit('SET_LOADING', true)
    try {
      const response = await getCurrentAccount()
      if (response.code === 200 && response.data) {
        commit('SET_CURRENT_ACCOUNT', response.data)
      }
    } catch (error) {
      console.error('加载账户失败:', error)
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 切换账户
  async switchAccount({ commit }, accountType) {
    commit('SET_LOADING', true)
    try {
      const response = await switchAccountType(accountType)
      if (response.code === 200 && response.data) {
        commit('SET_CURRENT_ACCOUNT', response.data)
        
        // 触发全局事件
        window.dispatchEvent(new CustomEvent('account-switched', {
          detail: { accountType, account: response.data }
        }))
      }
      return response
    } catch (error) {
      console.error('切换账户失败:', error)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },
  
  // 创建模拟账户
  async createSimulatedAccount({ dispatch }, data) {
    try {
      const response = await createAccount(data)
      if (response.code === 200) {
        // 创建成功后重新加载账户
        await dispatch('loadCurrentAccount')
      }
      return response
    } catch (error) {
      console.error('创建模拟账户失败:', error)
      throw error
    }
  },
  
  // 重置模拟账户
  async resetSimulatedAccount({ commit, state }, accountId) {
    try {
      const response = await resetAccount(accountId)
      if (response.code === 200) {
        // 重置成功后更新当前账户信息
        if (state.currentAccount && state.currentAccount.id === accountId) {
          const accountResponse = await getCurrentAccount()
          if (accountResponse.code === 200) {
            commit('SET_CURRENT_ACCOUNT', accountResponse.data)
          }
        }
        
        // 触发全局事件
        window.dispatchEvent(new CustomEvent('account-reset', {
          detail: { accountId }
        }))
      }
      return response
    } catch (error) {
      console.error('重置账户失败:', error)
      throw error
    }
  },
  
  // 清空账户信息
  clearAccounts({ commit }) {
    commit('CLEAR_ACCOUNTS')
  }
}

const getters = {
  // 当前账户可用资金
  availableCash: state => {
    return state.currentAccount ? state.currentAccount.available_cash : 0
  },
  
  // 当前账户总资产
  totalAssets: state => {
    return state.currentAccount ? state.currentAccount.total_assets : 0
  },
  
  // 是否有真实账户
  hasRealAccount: state => {
    return !!state.realAccount
  },
  
  // 是否有模拟账户
  hasSimulatedAccount: state => {
    return !!state.simulatedAccount
  },
  
  // 账户盈亏信息
  accountPnL: state => {
    if (!state.currentAccount) return null
    
    return {
      total: state.currentAccount.total_profit_loss,
      totalRate: state.currentAccount.total_profit_rate,
      daily: state.currentAccount.day_profit_loss,
      dailyRate: state.currentAccount.day_profit_rate
    }
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}