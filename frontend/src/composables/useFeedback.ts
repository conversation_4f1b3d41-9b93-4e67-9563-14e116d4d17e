/**
 * 用户反馈组合函数
 * 提供统一的用户反馈机制
 */
import { ref } from 'vue'
import { ElMessage, ElNotification, ElMessageBox, ElLoading } from 'element-plus'

export interface FeedbackOptions {
  duration?: number
  showClose?: boolean
  center?: boolean
  customClass?: string
}

export interface LoadingOptions {
  text?: string
  spinner?: string
  background?: string
  customClass?: string
}

export const useFeedback = () => {
  const isLoading = ref(false)
  const loadingInstance = ref<any>(null)

  // 成功消息
  const showSuccess = (message: string, options?: FeedbackOptions) => {
    ElMessage.success({
      message,
      duration: options?.duration ?? 3000,
      showClose: options?.showClose ?? true,
      center: options?.center ?? false,
      customClass: options?.customClass
    })
  }

  // 错误消息
  const showError = (message: string, options?: FeedbackOptions) => {
    ElMessage.error({
      message,
      duration: options?.duration ?? 5000,
      showClose: options?.showClose ?? true,
      center: options?.center ?? false,
      customClass: options?.customClass
    })
  }

  // 警告消息
  const showWarning = (message: string, options?: FeedbackOptions) => {
    ElMessage.warning({
      message,
      duration: options?.duration ?? 4000,
      showClose: options?.showClose ?? true,
      center: options?.center ?? false,
      customClass: options?.customClass
    })
  }

  // 信息消息
  const showInfo = (message: string, options?: FeedbackOptions) => {
    ElMessage.info({
      message,
      duration: options?.duration ?? 3000,
      showClose: options?.showClose ?? true,
      center: options?.center ?? false,
      customClass: options?.customClass
    })
  }

  // 成功通知
  const notifySuccess = (title: string, message?: string, options?: FeedbackOptions) => {
    ElNotification.success({
      title,
      message,
      duration: options?.duration ?? 4500,
      customClass: options?.customClass
    })
  }

  // 错误通知
  const notifyError = (title: string, message?: string, options?: FeedbackOptions) => {
    ElNotification.error({
      title,
      message,
      duration: options?.duration ?? 6000,
      customClass: options?.customClass
    })
  }

  // 警告通知
  const notifyWarning = (title: string, message?: string, options?: FeedbackOptions) => {
    ElNotification.warning({
      title,
      message,
      duration: options?.duration ?? 5000,
      customClass: options?.customClass
    })
  }

  // 信息通知
  const notifyInfo = (title: string, message?: string, options?: FeedbackOptions) => {
    ElNotification.info({
      title,
      message,
      duration: options?.duration ?? 4000,
      customClass: options?.customClass
    })
  }

  // 确认对话框
  const confirm = async (
    message: string,
    title: string = '确认',
    options?: {
      confirmButtonText?: string
      cancelButtonText?: string
      type?: 'warning' | 'info' | 'success' | 'error'
    }
  ): Promise<boolean> => {
    try {
      await ElMessageBox.confirm(message, title, {
        confirmButtonText: options?.confirmButtonText ?? '确定',
        cancelButtonText: options?.cancelButtonText ?? '取消',
        type: options?.type ?? 'warning'
      })
      return true
    } catch {
      return false
    }
  }

  // 输入对话框
  const prompt = async (
    message: string,
    title: string = '输入',
    options?: {
      confirmButtonText?: string
      cancelButtonText?: string
      inputPattern?: RegExp
      inputErrorMessage?: string
      inputPlaceholder?: string
      inputType?: string
    }
  ): Promise<string | null> => {
    try {
      const { value } = await ElMessageBox.prompt(message, title, {
        confirmButtonText: options?.confirmButtonText ?? '确定',
        cancelButtonText: options?.cancelButtonText ?? '取消',
        inputPattern: options?.inputPattern,
        inputErrorMessage: options?.inputErrorMessage,
        inputPlaceholder: options?.inputPlaceholder,
        inputType: options?.inputType ?? 'text'
      })
      return value
    } catch {
      return null
    }
  }

  // 显示加载
  const showLoading = (options?: LoadingOptions) => {
    if (loadingInstance.value) {
      hideLoading()
    }

    isLoading.value = true
    loadingInstance.value = ElLoading.service({
      text: options?.text ?? '加载中...',
      spinner: options?.spinner,
      background: options?.background ?? 'rgba(0, 0, 0, 0.7)',
      customClass: options?.customClass
    })

    return loadingInstance.value
  }

  // 隐藏加载
  const hideLoading = () => {
    if (loadingInstance.value) {
      loadingInstance.value.close()
      loadingInstance.value = null
    }
    isLoading.value = false
  }

  // 异步操作包装器
  const withLoading = async <T>(
    operation: () => Promise<T>,
    loadingText?: string
  ): Promise<T> => {
    showLoading({ text: loadingText })
    try {
      const result = await operation()
      return result
    } finally {
      hideLoading()
    }
  }

  // 异步操作包装器 - 带成功提示
  const withLoadingAndSuccess = async <T>(
    operation: () => Promise<T>,
    successMessage: string,
    loadingText?: string
  ): Promise<T> => {
    const result = await withLoading(operation, loadingText)
    showSuccess(successMessage)
    return result
  }

  // 异步操作包装器 - 带错误处理
  const withLoadingAndErrorHandling = async <T>(
    operation: () => Promise<T>,
    errorMessage?: string,
    loadingText?: string
  ): Promise<T | null> => {
    try {
      return await withLoading(operation, loadingText)
    } catch (error: any) {
      showError(errorMessage ?? error.message ?? '操作失败')
      return null
    }
  }

  // 网络状态反馈
  const showNetworkStatus = () => {
    const updateOnlineStatus = () => {
      if (navigator.onLine) {
        showSuccess('网络连接已恢复')
      } else {
        showWarning('网络连接已断开，请检查网络设置')
      }
    }

    window.addEventListener('online', () => {
      showSuccess('网络连接已恢复')
    })

    window.addEventListener('offline', () => {
      showWarning('网络连接已断开，请检查网络设置')
    })

    return updateOnlineStatus
  }

  // 复制成功反馈
  const showCopySuccess = (text?: string) => {
    showSuccess(text ? `已复制: ${text}` : '内容已复制到剪贴板')
  }

  // 保存成功反馈
  const showSaveSuccess = (itemName?: string) => {
    showSuccess(itemName ? `${itemName}保存成功` : '保存成功')
  }

  // 删除确认
  const confirmDelete = async (itemName?: string): Promise<boolean> => {
    return await confirm(
      itemName ? `确定要删除${itemName}吗？` : '确定要删除吗？',
      '删除确认',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
  }

  return {
    isLoading,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    notifySuccess,
    notifyError,
    notifyWarning,
    notifyInfo,
    confirm,
    prompt,
    showLoading,
    hideLoading,
    withLoading,
    withLoadingAndSuccess,
    withLoadingAndErrorHandling,
    showNetworkStatus,
    showCopySuccess,
    showSaveSuccess,
    confirmDelete
  }
}

// 全局反馈实例
let globalFeedback: ReturnType<typeof useFeedback> | null = null

export const getGlobalFeedback = () => {
  if (!globalFeedback) {
    globalFeedback = useFeedback()
  }
  return globalFeedback
}