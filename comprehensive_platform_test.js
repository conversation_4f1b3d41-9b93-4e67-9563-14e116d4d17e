const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

/**
 * 量化投资平台深度用户体验测试
 * 模拟真实用户操作流程，发现潜在问题
 */

class QuantPlatformTester {
    constructor() {
        this.browser = null;
        this.page = null;
        this.testResults = [];
        this.screenshots = [];
        this.errors = [];
        this.baseUrl = 'http://localhost:5174';
        this.apiUrl = 'http://localhost:8000';
        
        // 测试配置
        this.config = {
            headless: false, // 设为false以便观察测试过程
            slowMo: 100,     // 减慢操作速度以便观察
            timeout: 30000,  // 30秒超时
            viewport: {
                width: 1920,
                height: 1080
            }
        };
        
        // 测试用户数据
        this.testUser = {
            username: 'test_user_' + Date.now(),
            email: 'test' + Date.now() + '@example.com',
            password: 'TestPassword123!',
            phone: '13800138000'
        };
    }

    async init() {
        console.log('🚀 启动量化投资平台深度测试...');
        
        this.browser = await puppeteer.launch({
            headless: this.config.headless,
            slowMo: this.config.slowMo,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu'
            ]
        });
        
        this.page = await this.browser.newPage();
        await this.page.setViewport(this.config.viewport);
        
        // 设置请求拦截和监控
        await this.setupRequestMonitoring();
        
        // 设置错误监听
        this.page.on('error', (error) => {
            this.logError('页面错误', error);
        });
        
        this.page.on('pageerror', (error) => {
            this.logError('页面脚本错误', error);
        });
        
        console.log('✅ 浏览器初始化完成');
    }

    async setupRequestMonitoring() {
        await this.page.setRequestInterception(true);
        
        this.page.on('request', (request) => {
            // 记录API请求
            if (request.url().includes('/api/')) {
                console.log(`📡 API请求: ${request.method()} ${request.url()}`);
            }
            request.continue();
        });
        
        this.page.on('response', (response) => {
            // 记录API响应
            if (response.url().includes('/api/')) {
                const status = response.status();
                const statusText = status >= 400 ? '❌' : '✅';
                console.log(`📡 API响应: ${statusText} ${status} ${response.url()}`);
                
                if (status >= 400) {
                    this.logError('API错误', {
                        url: response.url(),
                        status: status,
                        statusText: response.statusText()
                    });
                }
            }
        });
    }

    async takeScreenshot(name, description = '') {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `screenshot_${timestamp}_${name}.png`;
        const filepath = path.join(__dirname, 'screenshots', filename);
        
        // 确保screenshots目录存在
        const screenshotDir = path.join(__dirname, 'screenshots');
        if (!fs.existsSync(screenshotDir)) {
            fs.mkdirSync(screenshotDir, { recursive: true });
        }
        
        await this.page.screenshot({ 
            path: filepath, 
            fullPage: true 
        });
        
        this.screenshots.push({
            name,
            description,
            filename,
            filepath,
            timestamp: new Date()
        });
        
        console.log(`📸 截图保存: ${filename} - ${description}`);
    }

    logError(type, error) {
        const errorInfo = {
            type,
            error: error.toString(),
            stack: error.stack || '',
            timestamp: new Date(),
            url: this.page.url()
        };
        
        this.errors.push(errorInfo);
        console.error(`❌ ${type}:`, error);
    }

    logTestResult(testName, success, details = '', duration = 0) {
        const result = {
            testName,
            success,
            details,
            duration,
            timestamp: new Date(),
            url: this.page.url()
        };
        
        this.testResults.push(result);
        
        const status = success ? '✅' : '❌';
        const time = duration > 0 ? ` (${duration}ms)` : '';
        console.log(`${status} ${testName}${time}: ${details}`);
    }

    async waitForElement(selector, timeout = this.config.timeout) {
        try {
            await this.page.waitForSelector(selector, { timeout });
            return true;
        } catch (error) {
            this.logError(`等待元素失败: ${selector}`, error);
            return false;
        }
    }

    async clickElement(selector, description = '') {
        try {
            await this.page.waitForSelector(selector, { timeout: 10000 });
            await this.page.click(selector);
            console.log(`🖱️  点击: ${description || selector}`);
            await this.page.waitForTimeout(1000); // 等待动画完成
            return true;
        } catch (error) {
            this.logError(`点击失败: ${selector}`, error);
            return false;
        }
    }

    async typeText(selector, text, description = '') {
        try {
            await this.page.waitForSelector(selector, { timeout: 10000 });
            await this.page.click(selector);
            await this.page.keyboard.down('Control');
            await this.page.keyboard.press('KeyA');
            await this.page.keyboard.up('Control');
            await this.page.type(selector, text, { delay: 50 });
            console.log(`⌨️  输入: ${description || selector} = ${text}`);
            return true;
        } catch (error) {
            this.logError(`输入失败: ${selector}`, error);
            return false;
        }
    }

    // 测试首页加载
    async testHomePage() {
        console.log('\n🏠 测试首页加载...');
        const startTime = Date.now();
        
        try {
            await this.page.goto(this.baseUrl, { 
                waitUntil: 'networkidle2',
                timeout: this.config.timeout 
            });
            
            await this.takeScreenshot('homepage_loaded', '首页加载完成');
            
            // 检查关键元素
            const checks = [
                { selector: 'body', name: '页面主体' },
                { selector: 'nav, .navbar, .header', name: '导航栏' },
                { selector: '.login, [data-test="login"], button:contains("登录")', name: '登录按钮' }
            ];
            
            let allChecksPass = true;
            for (const check of checks) {
                const exists = await this.page.$(check.selector);
                if (exists) {
                    console.log(`✅ 找到${check.name}`);
                } else {
                    console.log(`❌ 未找到${check.name}: ${check.selector}`);
                    allChecksPass = false;
                }
            }
            
            const duration = Date.now() - startTime;
            this.logTestResult('首页加载', allChecksPass, `页面加载耗时${duration}ms`, duration);
            
            return allChecksPass;
            
        } catch (error) {
            this.logError('首页加载失败', error);
            await this.takeScreenshot('homepage_error', '首页加载错误');
            this.logTestResult('首页加载', false, error.message);
            return false;
        }
    }

    // 测试用户注册
    async testUserRegistration() {
        console.log('\n📝 测试用户注册...');
        const startTime = Date.now();
        
        try {
            // 寻找注册按钮或链接
            const registerSelectors = [
                'a[href*="register"]',
                'button:contains("注册")',
                '.register',
                '[data-test="register"]',
                'a:contains("注册")',
                'a:contains("立即注册")'
            ];
            
            let registerFound = false;
            for (const selector of registerSelectors) {
                try {
                    const element = await this.page.$(selector);
                    if (element) {
                        await this.clickElement(selector, '注册按钮');
                        registerFound = true;
                        break;
                    }
                } catch (e) {
                    // 继续尝试下一个选择器
                }
            }
            
            if (!registerFound) {
                this.logTestResult('用户注册', false, '未找到注册入口');
                return false;
            }
            
            await this.page.waitForTimeout(2000);
            await this.takeScreenshot('register_page', '注册页面');
            
            // 填写注册表单
            const formFields = [
                { selector: 'input[name="username"], input[placeholder*="用户名"], #username', value: this.testUser.username, name: '用户名' },
                { selector: 'input[name="email"], input[type="email"], input[placeholder*="邮箱"], #email', value: this.testUser.email, name: '邮箱' },
                { selector: 'input[name="password"], input[type="password"], input[placeholder*="密码"], #password', value: this.testUser.password, name: '密码' },
                { selector: 'input[name="phone"], input[placeholder*="手机"], #phone', value: this.testUser.phone, name: '手机号' }
            ];
            
            let fieldsFilledCount = 0;
            for (const field of formFields) {
                const success = await this.typeText(field.selector, field.value, field.name);
                if (success) fieldsFilledCount++;
            }
            
            // 提交注册表单
            const submitSuccess = await this.clickElement('button[type="submit"], .submit, button:contains("注册"), button:contains("提交")', '提交注册');
            
            await this.page.waitForTimeout(3000);
            await this.takeScreenshot('register_result', '注册结果');
            
            const duration = Date.now() - startTime;
            const success = fieldsFilledCount >= 2 && submitSuccess;
            this.logTestResult('用户注册', success, `填写${fieldsFilledCount}个字段，提交${submitSuccess ? '成功' : '失败'}`, duration);
            
            return success;
            
        } catch (error) {
            this.logError('用户注册失败', error);
            await this.takeScreenshot('register_error', '注册错误');
            this.logTestResult('用户注册', false, error.message);
            return false;
        }
    }
}

    // 测试用户登录
    async testUserLogin() {
        console.log('\n🔐 测试用户登录...');
        const startTime = Date.now();

        try {
            // 寻找登录按钮或链接
            const loginSelectors = [
                'a[href*="login"]',
                'button:contains("登录")',
                '.login',
                '[data-test="login"]',
                'a:contains("登录")',
                '.login-btn'
            ];

            let loginFound = false;
            for (const selector of loginSelectors) {
                try {
                    const element = await this.page.$(selector);
                    if (element) {
                        await this.clickElement(selector, '登录按钮');
                        loginFound = true;
                        break;
                    }
                } catch (e) {
                    // 继续尝试下一个选择器
                }
            }

            if (!loginFound) {
                this.logTestResult('用户登录', false, '未找到登录入口');
                return false;
            }

            await this.page.waitForTimeout(2000);
            await this.takeScreenshot('login_page', '登录页面');

            // 填写登录表单
            const usernameSuccess = await this.typeText(
                'input[name="username"], input[name="email"], input[placeholder*="用户名"], input[placeholder*="邮箱"], #username, #email',
                this.testUser.username,
                '用户名/邮箱'
            );

            const passwordSuccess = await this.typeText(
                'input[name="password"], input[type="password"], input[placeholder*="密码"], #password',
                this.testUser.password,
                '密码'
            );

            // 提交登录表单
            const submitSuccess = await this.clickElement(
                'button[type="submit"], .submit, button:contains("登录"), button:contains("提交")',
                '提交登录'
            );

            await this.page.waitForTimeout(3000);
            await this.takeScreenshot('login_result', '登录结果');

            const duration = Date.now() - startTime;
            const success = usernameSuccess && passwordSuccess && submitSuccess;
            this.logTestResult('用户登录', success, `表单填写${success ? '成功' : '失败'}`, duration);

            return success;

        } catch (error) {
            this.logError('用户登录失败', error);
            await this.takeScreenshot('login_error', '登录错误');
            this.logTestResult('用户登录', false, error.message);
            return false;
        }
    }

    // 测试市场数据页面
    async testMarketData() {
        console.log('\n📊 测试市场数据页面...');
        const startTime = Date.now();

        try {
            // 寻找市场数据入口
            const marketSelectors = [
                'a[href*="market"]',
                'a:contains("市场")',
                'a:contains("行情")',
                '.market',
                '[data-test="market"]',
                'nav a:contains("市场数据")'
            ];

            let marketFound = false;
            for (const selector of marketSelectors) {
                try {
                    const element = await this.page.$(selector);
                    if (element) {
                        await this.clickElement(selector, '市场数据入口');
                        marketFound = true;
                        break;
                    }
                } catch (e) {
                    // 继续尝试下一个选择器
                }
            }

            if (!marketFound) {
                this.logTestResult('市场数据', false, '未找到市场数据入口');
                return false;
            }

            await this.page.waitForTimeout(3000);
            await this.takeScreenshot('market_page', '市场数据页面');

            // 检查市场数据关键元素
            const marketElements = [
                { selector: '.stock-list, .market-list, table', name: '股票列表' },
                { selector: '.chart, canvas, #chart', name: '图表' },
                { selector: '.price, .stock-price', name: '价格信息' },
                { selector: '.search, input[placeholder*="搜索"], input[placeholder*="股票"]', name: '搜索功能' }
            ];

            let elementsFound = 0;
            for (const element of marketElements) {
                const exists = await this.page.$(element.selector);
                if (exists) {
                    console.log(`✅ 找到${element.name}`);
                    elementsFound++;
                } else {
                    console.log(`❌ 未找到${element.name}`);
                }
            }

            // 测试搜索功能
            const searchInput = await this.page.$('input[placeholder*="搜索"], input[placeholder*="股票"], .search input');
            if (searchInput) {
                await this.typeText('input[placeholder*="搜索"], input[placeholder*="股票"], .search input', '000001', '搜索股票');
                await this.page.waitForTimeout(2000);
                await this.takeScreenshot('market_search', '市场搜索结果');
            }

            const duration = Date.now() - startTime;
            const success = elementsFound >= 2;
            this.logTestResult('市场数据', success, `找到${elementsFound}个关键元素`, duration);

            return success;

        } catch (error) {
            this.logError('市场数据测试失败', error);
            await this.takeScreenshot('market_error', '市场数据错误');
            this.logTestResult('市场数据', false, error.message);
            return false;
        }
    }

    // 测试交易功能
    async testTrading() {
        console.log('\n💰 测试交易功能...');
        const startTime = Date.now();

        try {
            // 寻找交易入口
            const tradingSelectors = [
                'a[href*="trading"]',
                'a[href*="trade"]',
                'a:contains("交易")',
                '.trading',
                '[data-test="trading"]',
                'nav a:contains("交易")'
            ];

            let tradingFound = false;
            for (const selector of tradingSelectors) {
                try {
                    const element = await this.page.$(selector);
                    if (element) {
                        await this.clickElement(selector, '交易入口');
                        tradingFound = true;
                        break;
                    }
                } catch (e) {
                    // 继续尝试下一个选择器
                }
            }

            if (!tradingFound) {
                this.logTestResult('交易功能', false, '未找到交易入口');
                return false;
            }

            await this.page.waitForTimeout(3000);
            await this.takeScreenshot('trading_page', '交易页面');

            // 检查交易关键元素
            const tradingElements = [
                { selector: '.buy-btn, button:contains("买入")', name: '买入按钮' },
                { selector: '.sell-btn, button:contains("卖出")', name: '卖出按钮' },
                { selector: 'input[placeholder*="数量"], input[placeholder*="股数"]', name: '数量输入' },
                { selector: 'input[placeholder*="价格"], input[placeholder*="委托价"]', name: '价格输入' },
                { selector: '.portfolio, .position, .holdings', name: '持仓信息' }
            ];

            let elementsFound = 0;
            for (const element of tradingElements) {
                const exists = await this.page.$(element.selector);
                if (exists) {
                    console.log(`✅ 找到${element.name}`);
                    elementsFound++;
                } else {
                    console.log(`❌ 未找到${element.name}`);
                }
            }

            // 尝试模拟交易操作（不实际提交）
            const stockInput = await this.page.$('input[placeholder*="股票"], input[placeholder*="代码"]');
            if (stockInput) {
                await this.typeText('input[placeholder*="股票"], input[placeholder*="代码"]', '000001', '股票代码');
            }

            const quantityInput = await this.page.$('input[placeholder*="数量"], input[placeholder*="股数"]');
            if (quantityInput) {
                await this.typeText('input[placeholder*="数量"], input[placeholder*="股数"]', '100', '交易数量');
            }

            await this.takeScreenshot('trading_form', '交易表单填写');

            const duration = Date.now() - startTime;
            const success = elementsFound >= 3;
            this.logTestResult('交易功能', success, `找到${elementsFound}个关键元素`, duration);

            return success;

        } catch (error) {
            this.logError('交易功能测试失败', error);
            await this.takeScreenshot('trading_error', '交易功能错误');
            this.logTestResult('交易功能', false, error.message);
            return false;
        }
    }

module.exports = QuantPlatformTester;
