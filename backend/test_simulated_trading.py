#!/usr/bin/env python3
"""
模拟交易系统测试脚本
测试账户创建、交易下单、持仓查询等功能
"""

import asyncio
import aiohttp
import json
from datetime import datetime
import random


class SimulatedTradingTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.token = None
        self.account_id = None
        
    async def login(self):
        """登录获取token"""
        print("\n1. 登录系统...")
        async with aiohttp.ClientSession() as session:
            # 先获取验证码
            async with session.get(f"{self.base_url}/api/v1/captcha/generate") as resp:
                captcha_data = await resp.json()
                print(f"验证码Key: {captcha_data['data']['key']}")
            
            # 登录
            login_data = {
                "username": "admin",
                "password": "admin123",
                "captcha_key": captcha_data['data']['key'],
                "captcha_code": "1234"  # 测试环境固定验证码
            }
            
            async with session.post(
                f"{self.base_url}/api/v1/auth/login",
                json=login_data
            ) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    self.token = result['data']['access_token']
                    print("✓ 登录成功")
                    return True
                else:
                    print(f"✗ 登录失败: {await resp.text()}")
                    return False
    
    async def create_simulated_account(self):
        """创建模拟账户"""
        print("\n2. 创建模拟账户...")
        headers = {"Authorization": f"Bearer {self.token}"}
        
        account_data = {
            "account_type": "SIMULATED",
            "initial_capital": 1000000.0,
            "created_reason": "USER_CREATE",
            "settings": {
                "allow_short": False,
                "allow_margin": False,
                "slippage_mode": "RANDOM",
                "execution_delay": 300,
                "partial_fill": True,
                "market_impact_factor": 0.0001
            }
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/api/v1/account-management/create",
                headers=headers,
                json=account_data
            ) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    self.account_id = result['data']['account_id']
                    print(f"✓ 创建模拟账户成功: {self.account_id}")
                    print(f"  初始资金: {result['data']['initial_capital']}")
                    return True
                else:
                    print(f"✗ 创建账户失败: {await resp.text()}")
                    return False
    
    async def get_account_list(self):
        """获取账户列表"""
        print("\n3. 获取账户列表...")
        headers = {"Authorization": f"Bearer {self.token}"}
        
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{self.base_url}/api/v1/account-management/list",
                headers=headers,
                params={"account_type": "SIMULATED"}
            ) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    accounts = result['data']['items']
                    print(f"✓ 找到 {len(accounts)} 个模拟账户:")
                    for acc in accounts:
                        print(f"  - {acc['account_id']}: 总资产={acc['total_assets']}, 可用={acc['available_cash']}")
                    
                    # 使用第一个账户
                    if accounts and not self.account_id:
                        self.account_id = accounts[0]['account_id']
                else:
                    print(f"✗ 获取账户列表失败: {await resp.text()}")
    
    async def get_market_quote(self, symbol):
        """获取行情数据"""
        headers = {"Authorization": f"Bearer {self.token}"}
        
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{self.base_url}/api/v1/market-fixed/quote/{symbol}",
                headers=headers
            ) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    return result['data']
                return None
    
    async def submit_order(self, symbol, direction, order_type, price, volume):
        """提交订单"""
        print(f"\n4. 提交{direction}单: {symbol} {volume}股 @ {price}")
        headers = {"Authorization": f"Bearer {self.token}"}
        
        order_data = {
            "account_id": self.account_id,
            "symbol": symbol,
            "direction": direction,
            "order_type": order_type,
            "price": price,
            "volume": volume
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/api/v1/simulated-trading/order/submit",
                headers=headers,
                json=order_data
            ) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    order_info = result['data']
                    print(f"✓ 订单提交成功:")
                    print(f"  订单ID: {order_info['order_id']}")
                    print(f"  状态: {order_info['status']}")
                    return order_info['order_id']
                else:
                    print(f"✗ 订单提交失败: {await resp.text()}")
                    return None
    
    async def get_orders(self):
        """查询订单"""
        print("\n5. 查询订单列表...")
        headers = {"Authorization": f"Bearer {self.token}"}
        
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{self.base_url}/api/v1/simulated-trading/orders",
                headers=headers,
                params={"account_id": self.account_id}
            ) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    orders = result['data']['items']
                    print(f"✓ 找到 {len(orders)} 个订单:")
                    for order in orders:
                        print(f"  - {order['order_id']}: {order['symbol']} {order['direction']} "
                              f"{order['volume']}@{order['price']} 状态={order['status']}")
                else:
                    print(f"✗ 查询订单失败: {await resp.text()}")
    
    async def get_positions(self):
        """查询持仓"""
        print("\n6. 查询持仓...")
        headers = {"Authorization": f"Bearer {self.token}"}
        
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{self.base_url}/api/v1/simulated-trading/positions",
                headers=headers,
                params={"account_id": self.account_id}
            ) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    positions = result['data']['positions']
                    print(f"✓ 持仓数量: {len(positions)}")
                    
                    if positions:
                        for pos in positions:
                            print(f"\n  {pos['symbol']} - {pos['name']}:")
                            print(f"    持仓: {pos['volume']}股")
                            print(f"    成本: {pos['avg_price']}")
                            print(f"    现价: {pos['current_price']}")
                            print(f"    盈亏: {pos['profit_loss']} ({pos['profit_rate']*100:.2f}%)")
                    
                    summary = result['data']['summary']
                    print(f"\n  汇总:")
                    print(f"    总市值: {summary['total_market_value']}")
                    print(f"    总盈亏: {summary['total_profit_loss']}")
                    print(f"    收益率: {summary['total_profit_rate']*100:.2f}%")
                else:
                    print(f"✗ 查询持仓失败: {await resp.text()}")
    
    async def get_account_detail(self):
        """查询账户详情"""
        print("\n7. 查询账户详情...")
        headers = {"Authorization": f"Bearer {self.token}"}
        
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{self.base_url}/api/v1/account-management/detail/{self.account_id}",
                headers=headers
            ) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    account = result['data']
                    print(f"✓ 账户 {account['account_id']}:")
                    print(f"  总资产: {account['assets']['total_assets']}")
                    print(f"  可用资金: {account['assets']['available_cash']}")
                    print(f"  股票市值: {account['assets']['market_value']}")
                    print(f"  总盈亏: {account['profit_loss']['total_profit_loss']}")
                    print(f"  总收益率: {account['profit_loss']['total_profit_rate']*100:.2f}%")
                    
                    if 'performance' in account:
                        print(f"\n  业绩指标:")
                        print(f"    夏普比率: {account['performance']['sharpe_ratio']}")
                        print(f"    最大回撤: {account['performance']['max_drawdown']*100:.2f}%")
                        print(f"    胜率: {account['performance']['win_rate']*100:.2f}%")
                else:
                    print(f"✗ 查询账户详情失败: {await resp.text()}")
    
    async def test_cancel_order(self, order_id):
        """测试撤单"""
        print(f"\n8. 测试撤单: {order_id}")
        headers = {"Authorization": f"Bearer {self.token}"}
        
        cancel_data = {
            "account_id": self.account_id,
            "order_id": order_id
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/api/v1/simulated-trading/order/cancel",
                headers=headers,
                json=cancel_data
            ) as resp:
                if resp.status == 200:
                    print("✓ 撤单成功")
                else:
                    print(f"✗ 撤单失败: {await resp.text()}")
    
    async def test_trading_scenario(self):
        """测试完整交易场景"""
        print("\n=== 模拟交易场景测试 ===")
        
        # 测试股票列表
        test_stocks = [
            {"symbol": "600000", "name": "浦发银行"},
            {"symbol": "600036", "name": "招商银行"},
            {"symbol": "000001", "name": "平安银行"},
            {"symbol": "000002", "name": "万科A"}
        ]
        
        # 执行一些买入操作
        print("\n执行买入操作...")
        for stock in test_stocks[:2]:
            # 获取行情
            quote = await self.get_market_quote(stock['symbol'])
            if quote:
                price = quote['current']
                volume = random.choice([100, 200, 300, 500])
                await self.submit_order(
                    stock['symbol'], 
                    "BUY", 
                    "LIMIT",
                    price,
                    volume
                )
                await asyncio.sleep(1)  # 等待订单处理
        
        # 等待订单成交
        print("\n等待订单成交...")
        await asyncio.sleep(3)
        
        # 查询持仓
        await self.get_positions()
        
        # 执行一些卖出操作
        print("\n执行卖出操作...")
        # 获取第一个持仓的股票进行卖出测试
        headers = {"Authorization": f"Bearer {self.token}"}
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{self.base_url}/api/v1/simulated-trading/positions",
                headers=headers,
                params={"account_id": self.account_id}
            ) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    positions = result['data']['positions']
                    if positions:
                        pos = positions[0]
                        sell_volume = min(100, pos['available_volume'])
                        if sell_volume > 0:
                            quote = await self.get_market_quote(pos['symbol'])
                            if quote:
                                await self.submit_order(
                                    pos['symbol'],
                                    "SELL",
                                    "LIMIT",
                                    quote['current'],
                                    sell_volume
                                )
        
        # 等待并查看最终结果
        await asyncio.sleep(3)
        await self.get_orders()
        await self.get_positions()
        await self.get_account_detail()
    
    async def run_tests(self):
        """运行所有测试"""
        print("开始测试模拟交易系统...")
        print("="*50)
        
        # 1. 登录
        if not await self.login():
            print("登录失败，测试终止")
            return
        
        # 2. 创建或获取模拟账户
        await self.get_account_list()
        if not self.account_id:
            await self.create_simulated_account()
        
        # 3. 执行交易场景测试
        await self.test_trading_scenario()
        
        print("\n" + "="*50)
        print("测试完成！")


async def main():
    tester = SimulatedTradingTester()
    await tester.run_tests()


if __name__ == "__main__":
    asyncio.run(main())