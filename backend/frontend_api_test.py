#!/usr/bin/env python3
"""
前端API测试 - 测试前端页面是否可访问以及API集成
"""

import requests
import time
from datetime import datetime

class FrontendAPITester:
    def __init__(self):
        self.frontend_url = "http://localhost:5174"
        self.backend_url = "http://localhost:8000"
        self.issues = []
        
    def log_issue(self, category, description, severity="medium"):
        """记录发现的问题"""
        issue = {
            "category": category,
            "description": description,
            "severity": severity,
            "timestamp": datetime.now().isoformat()
        }
        self.issues.append(issue)
        print(f"❌ [{severity.upper()}] {category}: {description}")
        
    def log_success(self, message):
        """记录成功的操作"""
        print(f"✅ {message}")
    
    def test_frontend_accessibility(self):
        """测试前端页面可访问性"""
        print("\n🌐 测试前端页面可访问性...")
        
        try:
            response = requests.get(self.frontend_url, timeout=10)
            
            if response.status_code == 200:
                self.log_success(f"前端页面可访问，状态码: {response.status_code}")
                
                # 检查HTML内容
                html_content = response.text
                
                # 检查基本HTML结构
                if "<html" in html_content and "</html>" in html_content:
                    self.log_success("HTML结构正常")
                else:
                    self.log_issue("前端页面", "HTML结构不完整", "medium")
                
                # 检查是否包含Vue.js相关内容
                if "vue" in html_content.lower() or "vite" in html_content.lower():
                    self.log_success("检测到Vue.js框架")
                else:
                    self.log_issue("前端页面", "未检测到Vue.js框架", "low")
                
                # 检查是否有错误信息
                if "error" in html_content.lower() or "404" in html_content:
                    self.log_issue("前端页面", "页面包含错误信息", "medium")
                
                return True
            else:
                self.log_issue("前端页面", f"页面访问失败，状态码: {response.status_code}", "high")
                return False
                
        except requests.exceptions.ConnectionError:
            self.log_issue("前端页面", "无法连接到前端服务", "high")
            return False
        except requests.exceptions.Timeout:
            self.log_issue("前端页面", "前端页面访问超时", "high")
            return False
        except Exception as e:
            self.log_issue("前端页面", f"前端页面访问异常: {str(e)}", "high")
            return False
    
    def test_backend_api_accessibility(self):
        """测试后端API可访问性"""
        print("\n🔗 测试后端API可访问性...")
        
        # 测试主要API端点
        api_endpoints = [
            ("/api/auth/register", "POST", "用户注册API"),
            ("/api/auth/login", "POST", "用户登录API"),
            ("/api/backtest", "POST", "回测创建API"),
            ("/docs", "GET", "API文档"),
            ("/", "GET", "根路径")
        ]
        
        accessible_apis = 0
        
        for endpoint, method, description in api_endpoints:
            try:
                url = f"{self.backend_url}{endpoint}"
                
                if method == "GET":
                    response = requests.get(url, timeout=5)
                else:
                    # 对于POST请求，只测试端点是否存在（不发送数据）
                    response = requests.post(url, json={}, timeout=5)
                
                # 检查响应状态
                if response.status_code in [200, 201, 422, 400]:  # 422和400表示端点存在但参数错误
                    self.log_success(f"{description}可访问")
                    accessible_apis += 1
                elif response.status_code == 405:
                    self.log_issue("API可访问性", f"{description}方法不允许", "medium")
                elif response.status_code == 404:
                    self.log_issue("API可访问性", f"{description}不存在", "high")
                else:
                    self.log_issue("API可访问性", f"{description}返回异常状态码: {response.status_code}", "medium")
                    
            except requests.exceptions.ConnectionError:
                self.log_issue("API可访问性", f"{description}连接失败", "high")
            except requests.exceptions.Timeout:
                self.log_issue("API可访问性", f"{description}访问超时", "medium")
            except Exception as e:
                self.log_issue("API可访问性", f"{description}访问异常: {str(e)}", "medium")
        
        if accessible_apis == 0:
            self.log_issue("API可访问性", "所有API都无法访问", "high")
            return False
        elif accessible_apis < len(api_endpoints):
            self.log_issue("API可访问性", f"部分API无法访问 ({accessible_apis}/{len(api_endpoints)})", "medium")
        
        return True
    
    def test_cors_configuration(self):
        """测试CORS配置"""
        print("\n🌍 测试CORS配置...")
        
        try:
            # 模拟前端向后端发送请求
            headers = {
                'Origin': self.frontend_url,
                'Access-Control-Request-Method': 'POST',
                'Access-Control-Request-Headers': 'Content-Type'
            }
            
            response = requests.options(f"{self.backend_url}/api/auth/login", headers=headers, timeout=5)
            
            if response.status_code == 200:
                # 检查CORS头
                cors_headers = response.headers
                
                if 'Access-Control-Allow-Origin' in cors_headers:
                    self.log_success("CORS配置正常")
                    
                    allowed_origin = cors_headers.get('Access-Control-Allow-Origin')
                    if allowed_origin == '*' or self.frontend_url in allowed_origin:
                        self.log_success("前端域名已允许访问")
                    else:
                        self.log_issue("CORS配置", f"前端域名未在允许列表中: {allowed_origin}", "medium")
                else:
                    self.log_issue("CORS配置", "缺少CORS头信息", "medium")
            else:
                self.log_issue("CORS配置", f"CORS预检请求失败: {response.status_code}", "medium")
                
        except Exception as e:
            self.log_issue("CORS配置", f"CORS测试异常: {str(e)}", "medium")
    
    def test_api_response_format(self):
        """测试API响应格式"""
        print("\n📋 测试API响应格式...")
        
        try:
            # 测试一个简单的GET请求
            response = requests.get(f"{self.backend_url}/docs", timeout=5)
            
            if response.status_code == 200:
                # 检查Content-Type
                content_type = response.headers.get('Content-Type', '')
                
                if 'text/html' in content_type:
                    self.log_success("API文档响应格式正确")
                else:
                    self.log_issue("API响应", f"API文档响应格式异常: {content_type}", "low")
            
            # 测试JSON API响应
            test_data = {"username": "test", "password": "test"}
            response = requests.post(f"{self.backend_url}/api/auth/login", json=test_data, timeout=5)
            
            # 检查响应是否为JSON
            try:
                json_data = response.json()
                self.log_success("API返回有效JSON格式")
                
                # 检查标准响应结构
                if isinstance(json_data, dict):
                    if 'success' in json_data or 'message' in json_data or 'data' in json_data:
                        self.log_success("API响应结构符合标准")
                    else:
                        self.log_issue("API响应", "API响应结构不标准", "low")
                else:
                    self.log_issue("API响应", "API响应不是字典格式", "medium")
                    
            except ValueError:
                self.log_issue("API响应", "API返回非JSON格式数据", "medium")
                
        except Exception as e:
            self.log_issue("API响应", f"API响应测试异常: {str(e)}", "medium")
    
    def test_performance(self):
        """测试性能"""
        print("\n⚡ 测试性能...")
        
        # 测试前端页面加载时间
        try:
            start_time = time.time()
            response = requests.get(self.frontend_url, timeout=10)
            frontend_load_time = time.time() - start_time
            
            if frontend_load_time < 2.0:
                self.log_success(f"前端页面加载快速: {frontend_load_time:.2f}秒")
            elif frontend_load_time < 5.0:
                self.log_issue("性能", f"前端页面加载较慢: {frontend_load_time:.2f}秒", "low")
            else:
                self.log_issue("性能", f"前端页面加载过慢: {frontend_load_time:.2f}秒", "medium")
                
        except Exception as e:
            self.log_issue("性能", f"前端性能测试失败: {str(e)}", "medium")
        
        # 测试API响应时间
        try:
            start_time = time.time()
            response = requests.get(f"{self.backend_url}/docs", timeout=10)
            api_response_time = time.time() - start_time
            
            if api_response_time < 1.0:
                self.log_success(f"API响应快速: {api_response_time:.2f}秒")
            elif api_response_time < 3.0:
                self.log_issue("性能", f"API响应较慢: {api_response_time:.2f}秒", "low")
            else:
                self.log_issue("性能", f"API响应过慢: {api_response_time:.2f}秒", "medium")
                
        except Exception as e:
            self.log_issue("性能", f"API性能测试失败: {str(e)}", "medium")
    
    def run_full_test(self):
        """运行完整测试"""
        print("🚀 开始前端API集成测试...")
        print("=" * 50)
        
        # 1. 前端页面可访问性测试
        frontend_ok = self.test_frontend_accessibility()
        
        # 2. 后端API可访问性测试
        backend_ok = self.test_backend_api_accessibility()
        
        # 3. CORS配置测试
        self.test_cors_configuration()
        
        # 4. API响应格式测试
        self.test_api_response_format()
        
        # 5. 性能测试
        self.test_performance()
        
        return frontend_ok and backend_ok
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 50)
        print("📋 前端API集成测试报告")
        print("=" * 50)
        
        if not self.issues:
            print("🎉 恭喜！前端和后端集成良好，未发现问题！")
        else:
            print(f"⚠️ 发现 {len(self.issues)} 个集成问题:")
            
            # 按严重程度分类
            high_issues = [i for i in self.issues if i["severity"] == "high"]
            medium_issues = [i for i in self.issues if i["severity"] == "medium"]
            low_issues = [i for i in self.issues if i["severity"] == "low"]
            
            if high_issues:
                print(f"\n🔴 高优先级问题 ({len(high_issues)}个):")
                for issue in high_issues:
                    print(f"  - {issue['category']}: {issue['description']}")
            
            if medium_issues:
                print(f"\n🟡 中优先级问题 ({len(medium_issues)}个):")
                for issue in medium_issues:
                    print(f"  - {issue['category']}: {issue['description']}")
            
            if low_issues:
                print(f"\n🟢 低优先级问题 ({len(low_issues)}个):")
                for issue in low_issues:
                    print(f"  - {issue['category']}: {issue['description']}")
        
        print("\n" + "=" * 50)

if __name__ == "__main__":
    tester = FrontendAPITester()
    
    try:
        success = tester.run_full_test()
        tester.generate_report()
        
        if success:
            print("✅ 前端API集成测试完成")
        else:
            print("❌ 前端API集成测试发现严重问题")
            
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        tester.generate_report()
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {str(e)}")
        tester.generate_report()
