"""
交易相关异步任务
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from celery import current_task
from sqlalchemy.ext.asyncio import AsyncSession

# WebSocket消息推送函数（待实现）
async def push_account_update(user_id: int, data: dict):
    """推送账户更新"""
    pass

async def push_order_update(user_id: int, data: dict):
    """推送订单更新"""
    pass

async def push_position_update(user_id: int, data: dict):
    """推送持仓更新"""
    pass

async def push_risk_alert(user_id: int, data: dict):
    """推送风险警报"""
    pass

async def push_trade_update(user_id: int, data: dict):
    """推送成交更新"""
    pass
from app.core.database import async_session
from app.db.models.trading import Order, Position, Trade
from app.schemas.trading import OrderData, OrderRequest, TradeData
from app.services.ctp_service import ctp_service
from app.services.risk_service import RiskService
from app.services.trading_service import TradingService
from app.utils.exceptions import DataNotFoundError

from .celery_app import celery_app

logger = logging.getLogger(__name__)


def run_async(coro):
    """Helper to run async code from a sync function like a Celery task."""
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            return asyncio.run_coroutine_threadsafe(coro, loop).result()
    except RuntimeError:
        pass
    return asyncio.run(coro)


async def _process_order_in_background(user_id: int, order_data: dict, task_id: str):
    """Internal async handler for order processing."""
    order_request = OrderRequest(**order_data)
    async with async_session() as session:
        trading_service = TradingService(db=session)
        order = await trading_service.submit_order(
            user_id=user_id, order_request=order_request
        )
        return OrderSchema.from_orm(order).model_dump()


@celery_app.task(bind=True, name="process_order_async")
def process_order_async(self, user_id: int, order_data: dict):
    """Celery task to process an order asynchronously."""
    try:
        self.update_state(state="PROGRESS", meta={"step": "starting"})
        order_result = run_async(
            _process_order_in_background(user_id, order_data, self.request.id)
        )
        self.update_state(state="SUCCESS", meta={"order": order_result})
        return order_result
    except Exception as e:
        logger.error(
            f"Order processing failed (task_id: {self.request.id}): {e}", exc_info=True
        )
        self.update_state(state="FAILURE", meta={"error": str(e)})
        run_async(
            push_risk_alert(
                user_id,
                {
                    "type": "order_error",
                    "message": f"订单处理失败: {str(e)}",
                    "timestamp": datetime.now().isoformat(),
                },
            )
        )
        raise


async def _process_trade_in_background(trade_data: dict):
    """Internal async handler for trade processing."""
    async with async_session() as session:
        trading_service = TradingService(db=session)
        trade = await trading_service.process_trade(trade_data=trade_data)
        # Note: Position and account updates are handled within process_trade
        return {
            "trade": TradeSchema.from_orm(trade).model_dump(),
        }


@celery_app.task(bind=True, name="process_trade_async")
def process_trade_async(self, trade_data: dict):
    """Celery task to process a trade asynchronously."""
    try:
        self.update_state(state="PROGRESS", meta={"step": "processing_trade"})
        user_id = trade_data.get("user_id")

        result = run_async(_process_trade_in_background(trade_data))

        self.update_state(state="SUCCESS", meta={"result": result})
        return result
    except Exception as e:
        logger.error(f"Trade processing failed: {e}", exc_info=True)
        self.update_state(state="FAILURE", meta={"error": str(e)})
        user_id = trade_data.get("user_id")
        if user_id:
            run_async(
                push_risk_alert(
                    user_id,
                    {
                        "type": "trade_error",
                        "message": f"成交处理失败: {str(e)}",
                        "timestamp": datetime.now().isoformat(),
                    },
                )
            )
        raise


async def _cancel_order_in_background(user_id: int, order_id: str):
    """Internal async handler for order cancellation."""
    async with async_session() as session:
        trading_service = TradingService(db=session)
        cancelled_order = await trading_service.cancel_order(order_id=order_id)
        return OrderSchema.from_orm(cancelled_order).model_dump()


@celery_app.task(bind=True, name="cancel_order_async")
def cancel_order_async(self, user_id: int, order_id: str):
    """Celery task to cancel an order asynchronously."""
    try:
        self.update_state(state="PROGRESS", meta={"step": "cancelling_order"})

        order_update = run_async(_cancel_order_in_background(user_id, order_id))

        self.update_state(state="SUCCESS", meta={"order": order_update})
        return order_update
    except (ValueError, DataNotFoundError) as e:
        logger.warning(
            f"Order cancellation failed for user {user_id}, order {order_id}: {e}"
        )
        self.update_state(state="FAILURE", meta={"error": str(e)})
        run_async(
            push_risk_alert(
                user_id,
                {
                    "type": "cancel_error",
                    "message": f"撤单失败: {str(e)}",
                    "timestamp": datetime.now().isoformat(),
                },
            )
        )
        # Do not re-raise for expected errors, let the client handle it
        return {"status": "FAILURE", "error": str(e)}
    except Exception as e:
        logger.error(f"Order cancellation failed unexpectedly: {e}", exc_info=True)
        self.update_state(state="FAILURE", meta={"error": str(e)})
        run_async(
            push_risk_alert(
                user_id,
                {
                    "type": "cancel_error",
                    "message": f"撤单失败: {str(e)}",
                    "timestamp": datetime.now().isoformat(),
                },
            )
        )
        raise


@celery_app.task(bind=True, name="generate_trading_report")
def generate_trading_report(self, user_id: int, date_from: str, date_to: str):
    """生成交易报告"""
    try:
        self.update_state(
            state="PROGRESS", meta={"step": "collecting_data", "progress": 10}
        )

        # 收集交易数据
        import time

        time.sleep(1)

        # 计算交易指标
        self.update_state(
            state="PROGRESS", meta={"step": "calculating_metrics", "progress": 30}
        )
        time.sleep(2)

        # 生成图表数据
        self.update_state(
            state="PROGRESS", meta={"step": "generating_charts", "progress": 60}
        )
        time.sleep(2)

        # 生成报告文件
        self.update_state(
            state="PROGRESS", meta={"step": "generating_report", "progress": 80}
        )
        time.sleep(1)

        # 模拟报告生成结果
        report = {
            "user_id": user_id,
            "period": f"{date_from} to {date_to}",
            "summary": {
                "total_trades": 156,
                "total_volume": 50000,
                "total_pnl": 12500.50,
                "win_rate": 0.65,
                "sharpe_ratio": 1.25,
                "max_drawdown": 0.08,
                "commission_paid": 245.30,
            },
            "daily_pnl": [],  # 这里应该填充实际的每日盈亏数据
            "trade_analysis": {
                "avg_win": 850.25,
                "avg_loss": -420.15,
                "largest_win": 2500.00,
                "largest_loss": -1200.00,
                "profit_factor": 1.85,
            },
            "risk_metrics": {
                "var_95": 1500.00,
                "var_99": 2800.00,
                "beta": 1.15,
                "alpha": 0.08,
            },
            "files": {
                "pdf_report": f"/reports/trading_{user_id}_{date_from}_{date_to}.pdf",
                "excel_data": f"/reports/trading_data_{user_id}_{date_from}_{date_to}.xlsx",
                "charts": {
                    "pnl_curve": f"/charts/pnl_{user_id}_{date_from}_{date_to}.png",
                    "drawdown": f"/charts/dd_{user_id}_{date_from}_{date_to}.png",
                },
            },
            "generated_at": datetime.now().isoformat(),
        }

        self.update_state(state="SUCCESS", meta={"progress": 100, "report": report})
        return report

    except Exception as e:
        logger.error(f"报告生成失败: {e}")
        self.update_state(state="FAILURE", meta={"error": str(e)})
        raise


@celery_app.task(bind=True, name="calculate_portfolio_metrics")
def calculate_portfolio_metrics(self, user_id: int):
    """计算投资组合指标"""
    try:
        self.update_state(
            state="PROGRESS", meta={"step": "loading_positions", "progress": 10}
        )

        # 加载持仓数据
        import time

        time.sleep(0.5)

        self.update_state(
            state="PROGRESS", meta={"step": "fetching_market_data", "progress": 30}
        )
        # 获取最新市场数据
        time.sleep(1)

        self.update_state(
            state="PROGRESS", meta={"step": "calculating_risk_metrics", "progress": 60}
        )
        # 计算风险指标
        time.sleep(1.5)

        self.update_state(
            state="PROGRESS", meta={"step": "generating_alerts", "progress": 80}
        )
        # 生成风险警告
        time.sleep(0.5)

        # 模拟计算结果
        metrics = {
            "portfolio_value": {
                "total_market_value": 1250000.00,
                "total_cost": 1165000.00,
                "total_pnl": 85000.50,
                "total_pnl_percent": 0.073,
            },
            "leverage_metrics": {
                "leverage_ratio": 1.25,
                "margin_used": 312500.00,
                "margin_available": 937500.00,
                "margin_ratio": 0.25,
            },
            "risk_metrics": {
                "var_95": 25000.00,
                "var_99": 45000.00,
                "expected_shortfall": 52000.00,
                "beta": 1.15,
                "alpha": 0.08,
                "tracking_error": 0.12,
            },
            "performance_metrics": {
                "sharpe_ratio": 1.45,
                "sortino_ratio": 1.68,
                "max_drawdown": 0.12,
                "calmar_ratio": 0.75,
                "information_ratio": 0.85,
            },
            "concentration_risk": {
                "top_5_concentration": 0.65,
                "sector_concentration": {
                    "technology": 0.35,
                    "finance": 0.25,
                    "energy": 0.20,
                    "others": 0.20,
                },
            },
            "alerts": [],
            "calculated_at": datetime.now().isoformat(),
        }

        # 生成风险警告
        if metrics["leverage_metrics"]["leverage_ratio"] > 2.0:
            metrics["alerts"].append(
                {
                    "type": "high_leverage",
                    "severity": "warning",
                    "message": "杠杆率过高，请注意风险控制",
                }
            )

        if metrics["risk_metrics"]["max_drawdown"] > 0.15:
            metrics["alerts"].append(
                {
                    "type": "high_drawdown",
                    "severity": "critical",
                    "message": "最大回撤过大，建议降低仓位",
                }
            )

        # 推送指标更新
        loop.run_until_complete(
            push_account_update(
                user_id,
                {
                    "portfolio_metrics": metrics,
                    "updated_at": datetime.now().isoformat(),
                },
            )
        )

        # 推送风险警告
        if metrics["alerts"]:
            for alert in metrics["alerts"]:
                loop.run_until_complete(push_risk_alert(user_id, alert))

        self.update_state(state="SUCCESS", meta={"progress": 100, "metrics": metrics})
        return metrics

    except Exception as e:
        logger.error(f"指标计算失败: {e}")
        self.update_state(state="FAILURE", meta={"error": str(e)})
        raise


@celery_app.task(name="cleanup_expired_orders")
def cleanup_expired_orders():
    """清理过期订单"""
    try:
        # 清理超过24小时的未成交订单
        cutoff_time = datetime.now() - timedelta(hours=24)

        # 这里应该连接数据库进行清理
        # 模拟清理过程
        cleaned_count = 0

        # 实际实现应该：
        # 1. 查询过期的未成交订单
        # 2. 调用CTP接口撤销这些订单
        # 3. 更新数据库状态
        # 4. 推送WebSocket通知

        logger.info(f"清理了 {cleaned_count} 个过期订单")
        return {"cleaned_orders": cleaned_count, "cutoff_time": cutoff_time.isoformat()}

    except Exception as e:
        logger.error(f"订单清理失败: {e}")
        raise


@celery_app.task(name="update_market_prices")
def update_market_prices():
    """更新市场价格"""
    try:
        # 获取最新市场价格并更新持仓盈亏
        # 这里应该调用行情服务

        updated_count = 0

        # 实际实现应该：
        # 1. 获取所有活跃持仓的合约
        # 2. 从行情服务获取最新价格
        # 3. 计算持仓盈亏
        # 4. 更新数据库
        # 5. 推送WebSocket更新

        logger.info(f"更新了 {updated_count} 个持仓的市场价格")
        return {
            "updated_positions": updated_count,
            "update_time": datetime.now().isoformat(),
        }

    except Exception as e:
        logger.error(f"价格更新失败: {e}")
        raise


@celery_app.task(name="risk_monitoring")
def risk_monitoring():
    """风险监控任务"""
    try:
        # 定期风险检查任务
        # 1. 检查所有用户的风险指标
        # 2. 识别超限情况
        # 3. 发送警告或自动处理

        risk_alerts = []

        # 模拟风险检查
        # 实际实现应该遍历所有活跃账户

        logger.info(f"风险监控完成，发现 {len(risk_alerts)} 个风险警告")
        return {
            "alerts_generated": len(risk_alerts),
            "check_time": datetime.now().isoformat(),
        }

    except Exception as e:
        logger.error(f"风险监控失败: {e}")
        raise


@celery_app.task(name="daily_settlement")
def daily_settlement():
    """每日结算任务"""
    try:
        # 每日结算处理
        # 1. 计算当日盈亏
        # 2. 更新账户资金
        # 3. 生成结算报告
        # 4. 发送通知

        settlement_date = datetime.now().date()
        processed_accounts = 0

        logger.info(f"每日结算完成，处理了 {processed_accounts} 个账户")
        return {
            "settlement_date": settlement_date.isoformat(),
            "processed_accounts": processed_accounts,
            "settlement_time": datetime.now().isoformat(),
        }

    except Exception as e:
        logger.error(f"每日结算失败: {e}")
        raise
