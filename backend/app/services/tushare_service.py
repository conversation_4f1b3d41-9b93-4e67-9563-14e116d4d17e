"""
Tushare数据服务
增强的Tushare数据获取服务，包含重试机制、超时处理和连接池
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional, Union
import pandas as pd
import tushare as ts
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
import aiohttp
import socket

from app.core.config import settings

logger = logging.getLogger(__name__)


class TushareConnectionError(Exception):
    """Tushare连接错误"""
    pass


class TushareRateLimitError(Exception):
    """Tushare频率限制错误"""
    pass


class EnhancedTushareService:
    """增强的Tushare数据服务"""
    
    def __init__(self):
        self.ts_pro = None
        self._last_request_time = 0
        self._min_request_interval = 0.2  # 最小请求间隔200ms
        self._initialize_api()
    
    def _initialize_api(self):
        """初始化Tushare API"""
        if not settings.TUSHARE_TOKEN:
            logger.warning("⚠️ 未配置TUSHARE_TOKEN，将使用模拟数据")
            return
        
        try:
            # 设置Token
            ts.set_token(settings.TUSHARE_TOKEN)
            
            # 设置超时时间
            import tushare.pro.data_pro as dp
            if hasattr(dp, 'DataApi'):
                # 清除可能的代理设置
                import requests
                requests.packages.urllib3.disable_warnings()
            
            self.ts_pro = ts.pro_api()
            
            # 测试连接
            self._test_connection()
            logger.info("✅ Tushare Pro API初始化成功")
            
        except Exception as e:
            logger.warning(f"⚠️ Tushare Pro API初始化失败，将在降级模式下运行: {e}")
            self.ts_pro = None
            # 不抛出异常，允许服务在没有Tushare连接的情况下启动
    
    def _test_connection(self):
        """测试Tushare连接"""
        if not self.ts_pro:
            return False
        
        try:
            # 简单的连接测试
            df = self.ts_pro.trade_cal(exchange='SSE', start_date='20241201', end_date='20241201')
            logger.info("🔗 Tushare连接测试成功")
            return True
        except Exception as e:
            logger.error(f"🔗 Tushare连接测试失败: {e}")
            raise TushareConnectionError(f"连接测试失败: {e}")
    
    def _rate_limit(self):
        """频率限制控制"""
        current_time = time.time()
        time_since_last = current_time - self._last_request_time
        
        if time_since_last < self._min_request_interval:
            sleep_time = self._min_request_interval - time_since_last
            logger.debug(f"频率限制: 等待 {sleep_time:.3f} 秒")
            time.sleep(sleep_time)
        
        self._last_request_time = time.time()
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((ConnectionError, TimeoutError, socket.timeout))
    )
    def _make_request(self, func, **kwargs):
        """带重试机制的请求"""
        if not self.ts_pro:
            raise TushareConnectionError("Tushare API未初始化")
        
        self._rate_limit()
        
        try:
            logger.debug(f"请求Tushare API: {func.__name__} with {kwargs}")
            result = func(**kwargs)
            logger.debug(f"请求成功，返回 {len(result) if hasattr(result, '__len__') else 'N/A'} 条记录")
            return result
            
        except Exception as e:
            error_msg = str(e).lower()
            
            # 处理不同类型的错误
            if "timeout" in error_msg or "connection" in error_msg:
                logger.warning(f"连接超时，正在重试: {e}")
                raise ConnectionError(f"Tushare连接超时: {e}")
            elif "rate limit" in error_msg or "频率" in error_msg:
                logger.warning(f"触发频率限制: {e}")
                time.sleep(1)  # 额外等待
                raise TushareRateLimitError(f"频率限制: {e}")
            else:
                logger.error(f"Tushare请求失败: {e}")
                raise
    
    async def get_stock_basic(self, exchange: str = "", list_status: str = "L") -> pd.DataFrame:
        """获取股票基本信息"""
        try:
            loop = asyncio.get_event_loop()
            df = await loop.run_in_executor(
                None, 
                self._make_request,
                self.ts_pro.stock_basic,
                exchange=exchange,
                list_status=list_status,
                fields="ts_code,symbol,name,area,industry,market,list_date"
            )
            return df
            
        except Exception as e:
            logger.error(f"获取股票基本信息失败: {e}")
            return pd.DataFrame()
    
    async def get_daily_data(self, ts_code: str, start_date: str = None, end_date: str = None, limit: int = None) -> pd.DataFrame:
        """获取日线数据"""
        try:
            loop = asyncio.get_event_loop()
            kwargs = {"ts_code": ts_code}
            if start_date:
                kwargs["start_date"] = start_date
            if end_date:
                kwargs["end_date"] = end_date
            if limit:
                kwargs["limit"] = limit
                
            df = await loop.run_in_executor(
                None,
                self._make_request,
                self.ts_pro.daily,
                **kwargs
            )
            return df
            
        except Exception as e:
            logger.error(f"获取日线数据失败 {ts_code}: {e}")
            return pd.DataFrame()
    
    async def get_index_daily(self, ts_code: str, start_date: str = None, end_date: str = None, limit: int = None) -> pd.DataFrame:
        """获取指数日线数据"""
        try:
            loop = asyncio.get_event_loop()
            kwargs = {"ts_code": ts_code}
            if start_date:
                kwargs["start_date"] = start_date
            if end_date:
                kwargs["end_date"] = end_date
            if limit:
                kwargs["limit"] = limit
                
            df = await loop.run_in_executor(
                None,
                self._make_request,
                self.ts_pro.index_daily,
                **kwargs
            )
            return df
            
        except Exception as e:
            logger.error(f"获取指数日线数据失败 {ts_code}: {e}")
            return pd.DataFrame()
    
    async def get_realtime_quotes(self, symbols: List[str]) -> Dict[str, Dict]:
        """获取实时行情（使用日线数据模拟）"""
        quotes = {}
        
        for symbol in symbols:
            try:
                # 获取最新的日线数据作为实时行情
                df = await self.get_daily_data(symbol, limit=1)
                
                if not df.empty:
                    row = df.iloc[0]
                    quotes[symbol] = {
                        "symbol": symbol,
                        "price": float(row["close"]),
                        "prev_close": float(row["pre_close"]),
                        "open": float(row["open"]),
                        "high": float(row["high"]),
                        "low": float(row["low"]),
                        "volume": float(row["vol"]) * 100,  # 转换为股
                        "amount": float(row["amount"]) * 1000,  # 转换为元
                        "change": float(row["change"]) if pd.notna(row["change"]) else 0,
                        "change_percent": float(row["pct_chg"]) if pd.notna(row["pct_chg"]) else 0,
                        "time": row["trade_date"],
                        "type": "stock"
                    }
                    
            except Exception as e:
                logger.warning(f"获取 {symbol} 行情失败: {e}")
                # 返回默认值
                quotes[symbol] = {
                    "symbol": symbol,
                    "price": 0,
                    "prev_close": 0,
                    "open": 0,
                    "high": 0,
                    "low": 0,
                    "volume": 0,
                    "amount": 0,
                    "change": 0,
                    "change_percent": 0,
                    "time": "",
                    "type": "stock"
                }
        
        return quotes
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        status = {
            "service": "tushare",
            "status": "unknown",
            "message": "",
            "timestamp": time.time(),
            "token_configured": bool(settings.TUSHARE_TOKEN),
            "api_initialized": bool(self.ts_pro)
        }
        
        if not self.ts_pro:
            status.update({
                "status": "error",
                "message": "Tushare API未初始化"
            })
            return status
        
        try:
            # 简单的健康检查请求
            df = await self.get_daily_data("000001.SZ", limit=1)
            
            if not df.empty:
                status.update({
                    "status": "healthy",
                    "message": "服务正常"
                })
            else:
                status.update({
                    "status": "warning",
                    "message": "API响应为空"
                })
                
        except Exception as e:
            status.update({
                "status": "error",
                "message": f"健康检查失败: {str(e)}"
            })
        
        return status


# 全局实例
_tushare_service = None


def get_tushare_service() -> EnhancedTushareService:
    """获取Tushare服务单例"""
    global _tushare_service
    if _tushare_service is None:
        _tushare_service = EnhancedTushareService()
    return _tushare_service


async def init_tushare_service():
    """初始化Tushare服务"""
    service = get_tushare_service()
    health = await service.health_check()
    logger.info(f"Tushare服务状态: {health}")
    return service