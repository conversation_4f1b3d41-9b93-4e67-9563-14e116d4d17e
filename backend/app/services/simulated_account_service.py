"""
模拟账户管理服务
"""
import uuid
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func, update, delete
from sqlalchemy.orm import selectinload

from app.models.trading import (
    Account, AccountType, Order, Trade, Position,
    SimulatedAccountStats, SimulatedExecution
)
from app.db.models.user import User
from app.core.exceptions import BusinessError, NotFoundError
from app.core.logging import get_logger

logger = get_logger(__name__)


class SimulatedAccountService:
    """模拟账户服务"""
    
    async def create_simulated_account(
        self,
        user_id: int,
        initial_capital: float = 1000000.0,
        reason: str = "USER_CREATE",
        settings: Optional[Dict] = None,
        db: AsyncSession = None
    ) -> Account:
        """创建模拟账户"""
        try:
            # 检查用户是否存在
            user = await db.get(User, user_id)
            if not user:
                raise NotFoundError("用户不存在")
                
            # 生成账户ID
            account_id = f"SIM{user_id}{datetime.now().strftime('%Y%m%d%H%M%S')}"
            
            # 默认设置
            default_settings = {
                "allow_short": False,
                "allow_margin": False,
                "slippage_mode": "RANDOM",
                "execution_delay": 300,
                "partial_fill": True,
                "market_impact_factor": 0.0001
            }
            
            if settings:
                default_settings.update(settings)
                
            # 查找真实账户作为父账户
            stmt = select(Account).where(
                and_(
                    Account.user_id == user_id,
                    Account.account_type == AccountType.REAL
                )
            )
            real_account = await db.scalar(stmt)
            
            # 创建模拟账户
            simulated_account = Account(
                user_id=user_id,
                account_id=account_id,
                account_type=AccountType.SIMULATED,
                parent_account_id=real_account.id if real_account else None,
                initial_capital=initial_capital,
                available_cash=initial_capital,
                total_assets=initial_capital,
                created_reason=reason,
                simulated_settings=default_settings,
                status="ACTIVE"
            )
            
            db.add(simulated_account)
            await db.commit()
            await db.refresh(simulated_account)
            
            # 创建初始统计记录
            await self._create_initial_stats(simulated_account, db)
            
            logger.info(f"创建模拟账户成功: {account_id}")
            return simulated_account
            
        except Exception as e:
            logger.error(f"创建模拟账户失败: {str(e)}")
            await db.rollback()
            raise
            
    async def get_simulated_accounts(
        self,
        user_id: int,
        db: AsyncSession
    ) -> List[Account]:
        """获取用户的所有模拟账户"""
        stmt = select(Account).where(
            and_(
                Account.user_id == user_id,
                Account.account_type == AccountType.SIMULATED
            )
        ).order_by(Account.create_time.desc())
        
        result = await db.execute(stmt)
        return result.scalars().all()
        
    async def get_current_account(
        self,
        user_id: int,
        account_type: AccountType,
        db: AsyncSession
    ) -> Optional[Account]:
        """获取当前使用的账户"""
        stmt = select(Account).where(
            and_(
                Account.user_id == user_id,
                Account.account_type == account_type,
                Account.status == "ACTIVE"
            )
        ).order_by(Account.create_time.desc())
        
        result = await db.execute(stmt)
        return result.scalars().first()
        
    async def switch_account(
        self,
        user_id: int,
        account_type: AccountType,
        db: AsyncSession
    ) -> Account:
        """切换账户类型"""
        account = await self.get_current_account(user_id, account_type, db)
        
        if not account:
            if account_type == AccountType.SIMULATED:
                # 自动创建模拟账户
                account = await self.create_simulated_account(
                    user_id, reason="AUTO_CREATE", db=db
                )
            else:
                raise NotFoundError("未找到真实账户")
                
        return account
        
    async def reset_simulated_account(
        self,
        account_id: int,
        user_id: int,
        db: AsyncSession
    ) -> Account:
        """重置模拟账户"""
        try:
            # 获取账户
            stmt = select(Account).where(
                and_(
                    Account.id == account_id,
                    Account.user_id == user_id,
                    Account.account_type == AccountType.SIMULATED
                )
            )
            account = await db.scalar(stmt)
            
            if not account:
                raise NotFoundError("模拟账户不存在")
                
            # 删除所有相关数据
            # 1. 删除订单
            await db.execute(
                delete(Order).where(Order.user_id == user_id)
            )
            
            # 2. 删除成交
            await db.execute(
                delete(Trade).where(Trade.user_id == user_id)
            )
            
            # 3. 删除持仓
            await db.execute(
                delete(Position).where(Position.user_id == user_id)
            )
            
            # 4. 删除模拟成交记录
            await db.execute(
                delete(SimulatedExecution).where(
                    SimulatedExecution.account_id == account_id
                )
            )
            
            # 5. 删除统计数据
            await db.execute(
                delete(SimulatedAccountStats).where(
                    SimulatedAccountStats.account_id == account_id
                )
            )
            
            # 重置账户资金
            account.total_assets = account.initial_capital
            account.available_cash = account.initial_capital
            account.frozen_cash = 0
            account.market_value = 0
            account.total_profit_loss = 0
            account.total_profit_rate = 0
            account.day_profit_loss = 0
            account.day_profit_rate = 0
            account.commission = 0
            account.reset_count += 1
            
            await db.commit()
            await db.refresh(account)
            
            # 创建新的初始统计记录
            await self._create_initial_stats(account, db)
            
            logger.info(f"重置模拟账户成功: {account.account_id}")
            return account
            
        except Exception as e:
            logger.error(f"重置模拟账户失败: {str(e)}")
            await db.rollback()
            raise
            
    async def update_account_stats(
        self,
        account_id: int,
        db: AsyncSession
    ):
        """更新账户统计数据"""
        try:
            # 获取账户
            account = await db.get(Account, account_id)
            if not account or account.account_type != AccountType.SIMULATED:
                return
                
            # 计算当日统计
            today = datetime.now().date()
            
            # 获取当日成交
            stmt = select(Trade).where(
                and_(
                    Trade.user_id == account.user_id,
                    func.date(Trade.trade_time) == today
                )
            )
            trades = await db.execute(stmt)
            trades = trades.scalars().all()
            
            # 计算统计数据
            daily_pnl = 0
            total_trades = len(trades)
            winning_trades = 0
            total_commission = 0
            
            for trade in trades:
                total_commission += trade.commission
                # 简化计算，实际需要考虑持仓成本
                if trade.direction.value == "SELL":
                    # 这里需要计算实际盈亏
                    pass
                    
            # 更新或创建统计记录
            stmt = select(SimulatedAccountStats).where(
                and_(
                    SimulatedAccountStats.account_id == account_id,
                    SimulatedAccountStats.date == today
                )
            )
            stats = await db.scalar(stmt)
            
            if not stats:
                stats = SimulatedAccountStats(
                    account_id=account_id,
                    date=today
                )
                db.add(stats)
                
            stats.daily_pnl = account.day_profit_loss
            stats.daily_return = account.day_profit_rate
            stats.total_trades = total_trades
            stats.winning_trades = winning_trades
            stats.total_commission = total_commission
            stats.cumulative_return = account.total_profit_rate
            
            await db.commit()
            
        except Exception as e:
            logger.error(f"更新账户统计失败: {str(e)}")
            await db.rollback()
            
    async def get_account_performance(
        self,
        account_id: int,
        period: str = "1M",
        db: AsyncSession
    ) -> Dict[str, Any]:
        """获取账户业绩"""
        account = await db.get(Account, account_id)
        if not account:
            raise NotFoundError("账户不存在")
            
        # 计算时间范围
        end_date = datetime.now().date()
        if period == "1W":
            start_date = end_date - timedelta(days=7)
        elif period == "1M":
            start_date = end_date - timedelta(days=30)
        elif period == "3M":
            start_date = end_date - timedelta(days=90)
        elif period == "1Y":
            start_date = end_date - timedelta(days=365)
        else:
            start_date = account.create_time.date()
            
        # 获取统计数据
        stmt = select(SimulatedAccountStats).where(
            and_(
                SimulatedAccountStats.account_id == account_id,
                SimulatedAccountStats.date >= start_date,
                SimulatedAccountStats.date <= end_date
            )
        ).order_by(SimulatedAccountStats.date)
        
        result = await db.execute(stmt)
        stats = result.scalars().all()
        
        # 计算业绩指标
        total_return = account.total_profit_rate
        max_drawdown = self._calculate_max_drawdown(stats)
        sharpe_ratio = self._calculate_sharpe_ratio(stats)
        win_rate = self._calculate_win_rate(stats)
        
        # 获取最佳和最差交易
        best_trade, worst_trade = await self._get_best_worst_trades(
            account.user_id, start_date, db
        )
        
        return {
            'account_info': {
                'account_id': account.account_id,
                'initial_capital': account.initial_capital,
                'total_assets': account.total_assets,
                'available_cash': account.available_cash,
                'market_value': account.market_value
            },
            'performance': {
                'total_return': total_return,
                'annualized_return': self._annualize_return(total_return, start_date),
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'win_rate': win_rate
            },
            'trading_stats': {
                'total_trades': sum(s.total_trades for s in stats),
                'winning_trades': sum(s.winning_trades for s in stats),
                'total_commission': sum(s.total_commission for s in stats),
                'best_trade': best_trade,
                'worst_trade': worst_trade
            },
            'daily_stats': [
                {
                    'date': s.date.isoformat(),
                    'daily_pnl': s.daily_pnl,
                    'daily_return': s.daily_return,
                    'cumulative_return': s.cumulative_return
                }
                for s in stats
            ]
        }
        
    async def _create_initial_stats(self, account: Account, db: AsyncSession):
        """创建初始统计记录"""
        stats = SimulatedAccountStats(
            account_id=account.id,
            date=datetime.now().date(),
            daily_pnl=0,
            daily_return=0,
            total_trades=0,
            winning_trades=0,
            total_commission=0,
            cumulative_return=0,
            max_drawdown=0,
            win_rate=0
        )
        db.add(stats)
        await db.commit()
        
    def _calculate_max_drawdown(self, stats: List[SimulatedAccountStats]) -> float:
        """计算最大回撤"""
        if not stats:
            return 0
            
        peak = 0
        max_dd = 0
        
        for s in stats:
            value = 1 + s.cumulative_return
            if value > peak:
                peak = value
            dd = (peak - value) / peak if peak > 0 else 0
            max_dd = max(max_dd, dd)
            
        return max_dd
        
    def _calculate_sharpe_ratio(
        self, 
        stats: List[SimulatedAccountStats],
        risk_free_rate: float = 0.03
    ) -> float:
        """计算夏普比率"""
        if not stats or len(stats) < 2:
            return 0
            
        returns = [s.daily_return for s in stats]
        if not returns:
            return 0
            
        avg_return = sum(returns) / len(returns)
        
        # 计算标准差
        variance = sum((r - avg_return) ** 2 for r in returns) / len(returns)
        std_dev = variance ** 0.5
        
        if std_dev == 0:
            return 0
            
        # 年化处理
        annual_return = avg_return * 252
        annual_std = std_dev * (252 ** 0.5)
        
        return (annual_return - risk_free_rate) / annual_std
        
    def _calculate_win_rate(self, stats: List[SimulatedAccountStats]) -> float:
        """计算胜率"""
        total_trades = sum(s.total_trades for s in stats)
        winning_trades = sum(s.winning_trades for s in stats)
        
        if total_trades == 0:
            return 0
            
        return winning_trades / total_trades
        
    def _annualize_return(self, total_return: float, start_date: datetime.date) -> float:
        """年化收益率"""
        days = (datetime.now().date() - start_date).days
        if days <= 0:
            return 0
            
        years = days / 365
        if years <= 0:
            return 0
            
        return ((1 + total_return) ** (1 / years) - 1) if total_return > -1 else -1
        
    async def _get_best_worst_trades(
        self,
        user_id: int,
        start_date: datetime.date,
        db: AsyncSession
    ) -> tuple:
        """获取最佳和最差交易"""
        # 这里简化处理，实际需要计算每笔交易的盈亏
        stmt = select(Trade).where(
            and_(
                Trade.user_id == user_id,
                func.date(Trade.trade_time) >= start_date
            )
        )
        
        result = await db.execute(stmt)
        trades = result.scalars().all()
        
        if not trades:
            return None, None
            
        # 简化返回第一笔和最后一笔
        return trades[0], trades[-1] if len(trades) > 1 else None