"""
模拟交易引擎服务
"""
import asyncio
import random
from datetime import datetime
from typing import Optional, Dict, Any, List

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from app.db.models.trading import (
    Order, OrderStatus, OrderSide, OrderType,
    Account, Trade, Position, Portfolio
)
from app.services.mock_market_service import MockMarketService
from app.core.exceptions import BusinessLogicError
import logging

logger = logging.getLogger(__name__)


class SimulatedTradingEngine:
    """模拟交易引擎"""
    
    def __init__(self, market_service: MockMarketService = None):
        self.market_service = market_service or MockMarketService()
        self.execution_simulator = ExecutionSimulator()
        self.risk_checker = RiskChecker()
        self.commission_calculator = CommissionCalculator()
        
    async def process_order(
        self, 
        order: Order, 
        account: Account,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """处理模拟订单"""
        try:
            # 1. 确保是模拟账户
            if account.account_type != AccountType.SIMULATED:
                raise BusinessLogicError("只能在模拟账户中进行模拟交易")
                
            # 2. 风险检查
            risk_check = await self.risk_checker.check(order, account, db)
            if not risk_check['passed']:
                await self._reject_order(order, risk_check['reason'], db)
                return {
                    'success': False,
                    'order_id': order.order_id,
                    'reason': risk_check['reason']
                }
                
            # 3. 获取市场数据
            market_data = await self.market_service.get_realtime_quote(order.symbol)
            if not market_data:
                await self._reject_order(order, "无法获取市场数据", db)
                return {
                    'success': False,
                    'order_id': order.order_id,
                    'reason': "无法获取市场数据"
                }
                
            # 4. 交易规则检查
            rule_check = await self._check_trading_rules(order, market_data)
            if not rule_check['passed']:
                await self._reject_order(order, rule_check['reason'], db)
                return {
                    'success': False,
                    'order_id': order.order_id,
                    'reason': rule_check['reason']
                }
                
            # 5. 更新订单状态为已提交
            order.status = OrderStatus.SUBMITTED
            await db.commit()
            
            # 6. 模拟执行
            execution = await self.execution_simulator.simulate(
                order, market_data, account, db
            )
            
            if execution:
                # 7. 创建成交记录
                trade = await self._create_trade(order, execution, db)
                
                # 8. 更新账户
                await self._update_account(account, trade, db)
                
                # 9. 更新持仓
                await self._update_position(account, trade, db)
                
                # 10. 记录模拟成交详情
                await self._save_simulated_execution(order, execution, account, db)
                
                await db.commit()
                
                return {
                    'success': True,
                    'order_id': order.order_id,
                    'trade_id': trade.trade_id,
                    'execution': execution
                }
            else:
                return {
                    'success': False,
                    'order_id': order.order_id,
                    'reason': "订单未能成交"
                }
                
        except Exception as e:
            logger.error(f"模拟交易处理失败: {str(e)}")
            await db.rollback()
            raise
            
    async def _reject_order(self, order: Order, reason: str, db: AsyncSession):
        """拒绝订单"""
        order.status = OrderStatus.REJECTED
        order.message = reason
        await db.commit()
        
    async def _check_trading_rules(self, order: Order, market_data: Dict) -> Dict:
        """检查交易规则"""
        # 检查交易时间
        now = datetime.now()
        if not self._is_trading_time(now):
            return {'passed': False, 'reason': '非交易时间'}
            
        # 检查涨跌停
        if order.order_type == OrderType.LIMIT:
            pre_close = market_data.get('pre_close', 0)
            if pre_close > 0:
                limit_up = pre_close * 1.10
                limit_down = pre_close * 0.90
                if order.price > limit_up or order.price < limit_down:
                    return {'passed': False, 'reason': '委托价格超出涨跌停限制'}
                    
        # 检查最小交易单位（A股100股）
        if order.volume % 100 != 0:
            return {'passed': False, 'reason': '交易数量必须是100的整数倍'}
            
        return {'passed': True}
        
    def _is_trading_time(self, dt: datetime) -> bool:
        """检查是否在交易时间"""
        # A股交易时间: 9:30-11:30, 13:00-15:00
        if dt.weekday() >= 5:  # 周末
            return False
            
        time = dt.time()
        morning_start = datetime.strptime("09:30", "%H:%M").time()
        morning_end = datetime.strptime("11:30", "%H:%M").time()
        afternoon_start = datetime.strptime("13:00", "%H:%M").time()
        afternoon_end = datetime.strptime("15:00", "%H:%M").time()
        
        return (morning_start <= time <= morning_end) or \
               (afternoon_start <= time <= afternoon_end)
               
    async def _create_trade(
        self, 
        order: Order, 
        execution: Dict,
        db: AsyncSession
    ) -> Trade:
        """创建成交记录"""
        trade = Trade(
            trade_id=f"T{datetime.now().strftime('%Y%m%d%H%M%S')}{random.randint(1000, 9999)}",
            order_id=order.order_id,
            user_id=order.user_id,
            symbol=order.symbol,
            exchange=order.exchange,
            direction=order.direction,
            price=execution['price'],
            volume=execution['volume'],
            commission=execution['commission'],
            trade_time=datetime.now()
        )
        db.add(trade)
        
        # 更新订单状态
        order.traded_volume += execution['volume']
        if order.traded_volume >= order.volume:
            order.status = OrderStatus.ALL_TRADED
        else:
            order.status = OrderStatus.PARTTRADED
            
        total_amount = order.traded_volume * order.avg_price if order.avg_price > 0 else 0
        total_amount += execution['volume'] * execution['price']
        order.avg_price = total_amount / order.traded_volume
        order.commission += execution['commission']
        
        return trade
        
    async def _update_account(
        self, 
        account: Account, 
        trade: Trade,
        db: AsyncSession
    ):
        """更新账户资金"""
        amount = trade.price * trade.volume
        total_cost = amount + trade.commission
        
        if trade.direction == OrderSide.BUY:
            # 买入：扣除资金
            account.available_cash -= total_cost
            account.frozen_cash -= total_cost
        else:
            # 卖出：增加资金
            account.available_cash += (amount - trade.commission)
            
        account.commission += trade.commission
        
    async def _update_position(
        self,
        account: Account,
        trade: Trade,
        db: AsyncSession
    ):
        """更新持仓"""
        # 查找现有持仓
        stmt = select(Position).where(
            and_(
                Position.user_id == account.user_id,
                Position.symbol == trade.symbol
            )
        )
        position = await db.scalar(stmt)
        
        if trade.direction == OrderSide.BUY:
            if position:
                # 更新现有持仓
                total_cost = position.avg_price * position.volume + \
                           trade.price * trade.volume + trade.commission
                position.volume += trade.volume
                position.avg_price = total_cost / position.volume
                position.available_volume += trade.volume
            else:
                # 创建新持仓
                position = Position(
                    user_id=account.user_id,
                    symbol=trade.symbol,
                    exchange=trade.exchange,
                    volume=trade.volume,
                    available_volume=trade.volume,
                    frozen_volume=0,
                    avg_price=trade.price + trade.commission / trade.volume
                )
                db.add(position)
        else:
            # 卖出
            if position:
                position.volume -= trade.volume
                position.available_volume -= trade.volume
                if position.volume == 0:
                    await db.delete(position)
                    
    async def _save_simulated_execution(
        self,
        order: Order,
        execution: Dict,
        account: Account,
        db: AsyncSession
    ):
        """保存模拟成交详情"""
        sim_exec = SimulatedExecution(
            order_id=order.order_id,
            account_id=account.id,
            market_price=execution['market_price'],
            execution_price=execution['price'],
            slippage=execution['slippage'],
            market_depth=execution.get('market_depth'),
            execution_time=datetime.now(),
            execution_delay=execution.get('delay', 0),
            algorithm=execution.get('algorithm', 'MARKET'),
            impact_cost=execution.get('impact_cost', 0)
        )
        db.add(sim_exec)


class ExecutionSimulator:
    """成交模拟器"""
    
    async def simulate(
        self,
        order: Order,
        market_data: Dict,
        account: Account,
        db: AsyncSession
    ) -> Optional[Dict]:
        """模拟订单成交"""
        settings = account.simulated_settings or {}
        
        # 1. 获取当前市场价格
        market_price = market_data.get('current', market_data.get('last_price', 0))
        if market_price <= 0:
            return None
            
        # 2. 模拟执行延迟
        delay = settings.get('execution_delay', 300)
        if delay > 0:
            await asyncio.sleep(delay / 1000)
            
        # 3. 计算成交价格（含滑点）
        execution_price = self._calculate_execution_price(
            order, market_price, settings.get('slippage_mode', 'RANDOM')
        )
        
        # 4. 检查是否能成交
        if not self._can_execute(order, market_price, execution_price):
            return None
            
        # 5. 计算成交量
        executed_volume = self._calculate_executed_volume(
            order, market_data, settings.get('partial_fill', True)
        )
        
        if executed_volume <= 0:
            return None
            
        # 6. 计算手续费
        commission = self._calculate_commission(
            execution_price * executed_volume,
            order.direction
        )
        
        return {
            'price': execution_price,
            'volume': executed_volume,
            'commission': commission,
            'market_price': market_price,
            'slippage': abs(execution_price - market_price) / market_price,
            'delay': delay,
            'algorithm': 'MARKET' if order.order_type == OrderType.MARKET else 'LIMIT',
            'market_depth': market_data.get('depth')
        }
        
    def _calculate_execution_price(
        self,
        order: Order,
        market_price: float,
        slippage_mode: str
    ) -> float:
        """计算成交价格（含滑点）"""
        if order.order_type == OrderType.LIMIT:
            # 限价单按委托价成交
            return order.price
            
        # 市价单计算滑点
        if slippage_mode == 'FIXED':
            slippage = 0.001  # 固定0.1%滑点
        elif slippage_mode == 'RANDOM':
            slippage = random.uniform(0, 0.002)  # 随机0-0.2%
        else:  # MARKET_IMPACT
            # 基于订单量计算市场冲击
            slippage = min(0.005, order.volume / 1000000)  # 最大0.5%
            
        if order.direction == OrderSide.BUY:
            return market_price * (1 + slippage)
        else:
            return market_price * (1 - slippage)
            
    def _can_execute(
        self,
        order: Order,
        market_price: float,
        execution_price: float
    ) -> bool:
        """检查是否能成交"""
        if order.order_type == OrderType.MARKET:
            return True
            
        if order.order_type == OrderType.LIMIT:
            if order.direction == OrderSide.BUY:
                return execution_price <= order.price
            else:
                return execution_price >= order.price
                
        return False
        
    def _calculate_executed_volume(
        self,
        order: Order,
        market_data: Dict,
        partial_fill: bool
    ) -> int:
        """计算成交量"""
        remaining = order.volume - order.traded_volume
        
        if not partial_fill:
            return remaining
            
        # 模拟部分成交
        if order.order_type == OrderType.MARKET:
            # 市价单通常能全部成交
            return remaining
        else:
            # 限价单可能部分成交
            fill_rate = random.uniform(0.5, 1.0)
            return int(remaining * fill_rate / 100) * 100  # 确保是100的整数倍
            
    def _calculate_commission(self, amount: float, direction: OrderSide) -> float:
        """计算手续费"""
        # 券商佣金（万分之3，最低5元）
        commission = max(amount * 0.0003, 5.0)
        
        # 印花税（卖出千分之1）
        stamp_duty = amount * 0.001 if direction == OrderSide.SELL else 0
        
        # 过户费（万分之0.2）
        transfer_fee = amount * 0.00002
        
        # 证管费（万分之0.2）
        regulatory_fee = amount * 0.00002
        
        return round(commission + stamp_duty + transfer_fee + regulatory_fee, 2)


class RiskChecker:
    """风险检查器"""
    
    async def check(
        self,
        order: Order,
        account: Account,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """执行风险检查"""
        # 检查账户状态
        if account.status != 'ACTIVE':
            return {'passed': False, 'reason': '账户状态异常'}
            
        # 检查资金是否充足
        if order.direction == OrderSide.BUY:
            required = order.price * order.volume * 1.01  # 预留1%手续费
            if account.available_cash < required:
                return {'passed': False, 'reason': '可用资金不足'}
                
        # 检查持仓是否充足（卖出）
        if order.direction == OrderSide.SELL:
            stmt = select(Position).where(
                and_(
                    Position.user_id == account.user_id,
                    Position.symbol == order.symbol
                )
            )
            position = await db.scalar(stmt)
            
            if not position or position.available_volume < order.volume:
                return {'passed': False, 'reason': '可用持仓不足'}
                
        return {'passed': True}


class CommissionCalculator:
    """手续费计算器"""
    
    @staticmethod
    def calculate(amount: float, direction: OrderSide) -> Dict[str, float]:
        """计算真实的交易费用"""
        # 券商佣金（万分之3，最低5元）
        commission = max(amount * 0.0003, 5.0)
        
        # 印花税（卖出千分之1）
        stamp_duty = amount * 0.001 if direction == OrderSide.SELL else 0
        
        # 过户费（万分之0.2）
        transfer_fee = amount * 0.00002
        
        # 证管费（万分之0.2）
        regulatory_fee = amount * 0.00002
        
        return {
            'commission': round(commission, 2),
            'stamp_duty': round(stamp_duty, 2),
            'transfer_fee': round(transfer_fee, 2),
            'regulatory_fee': round(regulatory_fee, 2),
            'total': round(commission + stamp_duty + transfer_fee + regulatory_fee, 2)
        }