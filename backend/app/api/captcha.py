import base64
import logging
import secrets
from io import BytesIO

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

# 可选导入PIL模块，如果不存在则使用模拟数据
try:
    from PIL import Image, ImageDraw
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    # 创建模拟的Image和ImageDraw类
    class MockImage:
        @staticmethod
        def new(mode, size, color):
            return MockImageInstance()
        
    class MockImageDraw:
        @staticmethod
        def Draw(img):
            return MockDrawInstance()
            
    class MockImageInstance:
        def save(self, buffer, format):
            # 返回一个简单的图片数据
            buffer.write(b"fake_image_data")
            
    class MockDrawInstance:
        def rectangle(self, *args, **kwargs):
            pass
    
    Image = MockImage
    ImageDraw = MockImageDraw

router = APIRouter()
logger = logging.getLogger(__name__)

# 存储验证码答案（生产环境应使用Redis）
CAPTCHA_ANSWERS = {}


class SliderCaptchaData(BaseModel):
    id: str
    background_image: str
    slider_image: str
    y: int
    h: int


class SliderCaptchaResponse(BaseModel):
    success: bool
    data: SliderCaptchaData
    message: str


class VerificationData(BaseModel):
    id: str
    position: int


class VerificationResponse(BaseModel):
    success: bool
    data: dict
    message: str


def create_slider_captcha():
    """生成滑块验证码"""
    try:
        logger.info("创建滑块验证码：开始")
        
        # 如果PIL不可用，返回模拟数据
        if not PIL_AVAILABLE:
            logger.warning("PIL不可用，返回模拟验证码数据")
            captcha_id = secrets.token_hex(16)
            slider_x = secrets.randbelow(151) + 100  # 100-250
            slider_y = secrets.randbelow(81) + 20  # 20-100
            
            # 存储正确答案
            CAPTCHA_ANSWERS[captcha_id] = slider_x
            
            return SliderCaptchaData(
                id=captcha_id,
                background_image="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
                slider_image="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
                y=slider_y,
                h=40
            )
        
        # 1. 创建背景图
        bg_image = Image.new("RGB", (300, 150), (255, 255, 255))
        draw = ImageDraw.Draw(bg_image)
        logger.info("创建滑块验证码：背景图已创建")

        # 2. 随机确定滑块位置
        slider_x = secrets.randbelow(151) + 100  # 100-250
        slider_y = secrets.randbelow(81) + 20  # 20-100
        slider_size = 40
        logger.info(f"创建滑块验证码：滑块位置 {slider_x}, {slider_y}")

        # 3. 从背景图中裁剪出滑块
        box = (slider_x, slider_y, slider_x + slider_size, slider_y + slider_size)
        slider_image = bg_image.crop(box)
        logger.info("创建滑块验证码：滑块已裁剪")

        # 4. 在背景图上挖空
        draw.rectangle(box, fill=(255, 255, 255))
        draw.rectangle(box, outline=(0, 0, 0), width=1)
        logger.info("创建滑块验证码：背景图已挖空")

        # 5. 将图片转为Base64
        bg_io = BytesIO()
        bg_image.save(bg_io, "PNG")
        bg_base64 = (
            "data:image/png;base64," + base64.b64encode(bg_io.getvalue()).decode()
        )

        slider_io = BytesIO()
        slider_image.save(slider_io, "PNG")
        slider_base64 = (
            "data:image/png;base64," + base64.b64encode(slider_io.getvalue()).decode()
        )
        logger.info("创建滑块验证码：图片已转为Base64")

        return bg_base64, slider_base64, slider_x, slider_y, slider_size
    except Exception as e:
        logger.error(f"创建滑块验证码失败: {e}", exc_info=True)
        raise


@router.get("/slider", response_model=SliderCaptchaResponse, summary="获取滑块验证码")
async def get_slider_captcha():
    logger.info("收到获取滑块验证码的请求")
    try:
        background_image, slider_image, position, y_pos, slider_height = create_slider_captcha()
        captcha_id = "captcha_" + secrets.token_hex(8)
        CAPTCHA_ANSWERS[captcha_id] = position
        logger.info(f"成功创建验证码, ID: {captcha_id}")
        
        return {
            "success": True,
            "data": {
                "id": captcha_id,
                "background_image": background_image,
                "slider_image": slider_image,
                "y": y_pos,
                "h": slider_height
            },
            "message": "验证码生成成功"
        }
    except Exception as e:
        logger.error(f"[/slider] 端点处理异常: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="生成验证码时发生内部错误")


@router.post("/slider/verify", response_model=VerificationResponse, summary="校验滑块位置")
async def verify_slider_position(data: VerificationData):
    correct_position = CAPTCHA_ANSWERS.get(data.id)
    if correct_position is None:
        return {
            "success": False,
            "data": {},
            "message": "验证码ID无效或已过期"
        }

    # 允许一定的误差
    if abs(data.position - correct_position) <= 5:
        del CAPTCHA_ANSWERS[data.id]  # 验证成功后删除
        verification_token = "slider_token_" + secrets.token_hex(16)
        return {
            "success": True,
            "data": {
                "token": verification_token
            },
            "message": "验证成功"
        }
    else:
        return {
            "success": False,
            "data": {},
            "message": "验证失败，请重试"
        }
