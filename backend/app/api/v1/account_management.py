"""
账户管理API接口
提供真实账户和模拟账户的管理功能
"""

from typing import List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, Query, Body
from sqlalchemy import select, and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.trading import (
    Account, AccountType, Position, Order, Trade,
    SimulatedAccountStats, OrderStatus
)
from app.services.simulated_trading_engine import SimulatedTradingEngine
from app.services.mock_market_service import MockMarketService
from app.schemas.response import Response
from app.core.logging import logger
from pydantic import BaseModel, Field


router = APIRouter()


# Schema定义
class AccountCreateRequest(BaseModel):
    account_type: AccountType = Field(..., description="账户类型")
    account_name: Optional[str] = Field(None, description="账户名称")
    initial_capital: float = Field(1000000.0, description="初始资金", ge=10000, le=********)
    created_reason: str = Field("USER_CREATE", description="创建原因")
    settings: Optional[dict] = Field(None, description="账户设置")


class AccountSwitchRequest(BaseModel):
    account_id: str = Field(..., description="账户ID")


class AccountUpdateRequest(BaseModel):
    account_name: Optional[str] = Field(None, description="账户名称")
    settings: Optional[dict] = Field(None, description="账户设置")


class SimulatedSettingsUpdateRequest(BaseModel):
    allow_short: Optional[bool] = Field(None, description="允许做空")
    allow_margin: Optional[bool] = Field(None, description="允许融资融券")
    slippage_mode: Optional[str] = Field(None, description="滑点模式", regex="^(FIXED|RANDOM|MARKET_IMPACT)$")
    execution_delay: Optional[int] = Field(None, description="执行延迟(毫秒)", ge=0, le=5000)
    partial_fill: Optional[bool] = Field(None, description="允许部分成交")
    market_impact_factor: Optional[float] = Field(None, description="市场冲击系数", ge=0, le=0.01)


@router.get("/list", summary="获取账户列表")
async def get_account_list(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    account_type: Optional[AccountType] = Query(None, description="账户类型"),
    status: Optional[str] = Query(None, description="账户状态"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量")
):
    """获取用户的所有账户列表"""
    try:
        # 构建查询
        query = select(Account).where(Account.user_id == current_user.id)
        
        if account_type:
            query = query.where(Account.account_type == account_type)
        if status:
            query = query.where(Account.status == status)
        
        # 排序
        query = query.order_by(Account.create_time.desc())
        
        # 执行查询
        result = await db.execute(query)
        accounts = result.scalars().all()
        
        # 分页
        total = len(accounts)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        accounts = accounts[start_idx:end_idx]
        
        # 格式化返回数据
        account_list = []
        for account in accounts:
            # 计算今日盈亏
            today_pnl = 0
            today_pnl_rate = 0
            if account.account_type == AccountType.SIMULATED:
                # 获取今日统计
                today = datetime.now().date()
                stats_result = await db.execute(
                    select(SimulatedAccountStats).where(
                        and_(
                            SimulatedAccountStats.account_id == account.id,
                            SimulatedAccountStats.date == today
                        )
                    )
                )
                today_stats = stats_result.scalar_one_or_none()
                if today_stats:
                    today_pnl = today_stats.daily_pnl
                    today_pnl_rate = today_stats.daily_return
            
            account_data = {
                "account_id": account.account_id,
                "account_type": account.account_type.value,
                "total_assets": account.total_assets,
                "available_cash": account.available_cash,
                "frozen_cash": account.frozen_cash,
                "market_value": account.market_value,
                "total_profit_loss": account.total_profit_loss,
                "total_profit_rate": account.total_profit_rate,
                "day_profit_loss": today_pnl,
                "day_profit_rate": today_pnl_rate,
                "status": account.status,
                "currency": account.currency,
                "created_reason": account.created_reason if account.account_type == AccountType.SIMULATED else None,
                "reset_count": account.reset_count if account.account_type == AccountType.SIMULATED else None,
                "create_time": account.create_time.isoformat(),
                "update_time": account.update_time.isoformat()
            }
            account_list.append(account_data)
        
        return Response.success(
            data={
                "total": total,
                "page": page,
                "page_size": page_size,
                "items": account_list
            },
            message="获取账户列表成功"
        )
        
    except Exception as e:
        logger.error(f"获取账户列表失败: {e}")
        return Response.error(message=f"获取账户列表失败: {str(e)}")


@router.get("/detail/{account_id}", summary="获取账户详情")
async def get_account_detail(
    account_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取账户详细信息"""
    try:
        # 查询账户
        result = await db.execute(
            select(Account).where(
                and_(
                    Account.account_id == account_id,
                    Account.user_id == current_user.id
                )
            )
        )
        account = result.scalar_one_or_none()
        
        if not account:
            return Response.error(message="账户不存在", code=404)
        
        # 获取持仓数量
        position_count_result = await db.execute(
            select(func.count(Position.id)).where(
                and_(
                    Position.user_id == current_user.id,
                    Position.volume > 0
                )
            )
        )
        position_count = position_count_result.scalar() or 0
        
        # 获取今日成交数量
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        trade_count_result = await db.execute(
            select(func.count(Trade.id)).where(
                and_(
                    Trade.user_id == current_user.id,
                    Trade.trade_time >= today_start
                )
            )
        )
        today_trade_count = trade_count_result.scalar() or 0
        
        # 获取未完成订单数量
        pending_order_result = await db.execute(
            select(func.count(Order.id)).where(
                and_(
                    Order.user_id == current_user.id,
                    Order.status.in_([
                        OrderStatus.SUBMITTING,
                        OrderStatus.SUBMITTED,
                        OrderStatus.NOTTRADED,
                        OrderStatus.PARTTRADED
                    ])
                )
            )
        )
        pending_order_count = pending_order_result.scalar() or 0
        
        # 构建返回数据
        account_detail = {
            "account_id": account.account_id,
            "account_type": account.account_type.value,
            "status": account.status,
            "currency": account.currency,
            
            # 资金信息
            "assets": {
                "total_assets": account.total_assets,
                "available_cash": account.available_cash,
                "frozen_cash": account.frozen_cash,
                "market_value": account.market_value,
                "buying_power": account.available_cash  # 简化处理
            },
            
            # 盈亏信息
            "profit_loss": {
                "total_profit_loss": account.total_profit_loss,
                "total_profit_rate": account.total_profit_rate,
                "day_profit_loss": account.day_profit_loss,
                "day_profit_rate": account.day_profit_rate
            },
            
            # 交易统计
            "statistics": {
                "position_count": position_count,
                "today_trade_count": today_trade_count,
                "pending_order_count": pending_order_count,
                "total_commission": account.commission
            },
            
            # 模拟账户特有信息
            "simulated_info": {
                "initial_capital": account.initial_capital,
                "created_reason": account.created_reason,
                "reset_count": account.reset_count,
                "settings": account.simulated_settings
            } if account.account_type == AccountType.SIMULATED else None,
            
            # 时间信息
            "create_time": account.create_time.isoformat(),
            "update_time": account.update_time.isoformat()
        }
        
        # 如果是模拟账户，获取性能指标
        if account.account_type == AccountType.SIMULATED:
            # 获取最近30天的统计数据
            thirty_days_ago = datetime.now().date() - timedelta(days=30)
            stats_result = await db.execute(
                select(SimulatedAccountStats).where(
                    and_(
                        SimulatedAccountStats.account_id == account.id,
                        SimulatedAccountStats.date >= thirty_days_ago
                    )
                ).order_by(SimulatedAccountStats.date)
            )
            stats_list = stats_result.scalars().all()
            
            if stats_list:
                # 计算性能指标
                returns = [stat.daily_return for stat in stats_list if stat.daily_return is not None]
                
                # 夏普比率（简化计算）
                if returns and len(returns) > 1:
                    try:
                        import numpy as np
                        returns_array = np.array(returns)
                        sharpe_ratio = np.sqrt(252) * returns_array.mean() / returns_array.std() if returns_array.std() > 0 else 0
                    except ImportError:
                        # 如果没有numpy，使用简单计算
                        mean_return = sum(returns) / len(returns)
                        variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
                        std_dev = variance ** 0.5
                        sharpe_ratio = (252 ** 0.5) * mean_return / std_dev if std_dev > 0 else 0
                else:
                    sharpe_ratio = 0
                
                # 最大回撤
                max_drawdown = max([stat.max_drawdown for stat in stats_list if stat.max_drawdown is not None], default=0)
                
                # 胜率
                latest_stats = stats_list[-1]
                win_rate = latest_stats.win_rate if latest_stats else 0
                
                account_detail["performance"] = {
                    "sharpe_ratio": round(sharpe_ratio, 2),
                    "max_drawdown": round(max_drawdown, 4),
                    "win_rate": round(win_rate, 4),
                    "total_trades": sum([stat.total_trades for stat in stats_list])
                }
        
        return Response.success(data=account_detail, message="获取账户详情成功")
        
    except Exception as e:
        logger.error(f"获取账户详情失败: {e}")
        return Response.error(message=f"获取账户详情失败: {str(e)}")


@router.post("/create", summary="创建账户")
async def create_account(
    request: AccountCreateRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """创建新账户（主要用于创建模拟账户）"""
    try:
        if request.account_type == AccountType.SIMULATED:
            # 创建模拟账户
            market_service = MockMarketService()
            engine = SimulatedTradingEngine(market_service)
            
            account = await engine.create_simulated_account(
                db=db,
                user_id=current_user.id,
                initial_capital=request.initial_capital,
                account_name=request.account_name,
                created_reason=request.created_reason
            )
            
            # 更新设置
            if request.settings:
                account.simulated_settings.update(request.settings)
                await db.commit()
            
            return Response.success(
                data={
                    "account_id": account.account_id,
                    "account_type": account.account_type.value,
                    "initial_capital": account.initial_capital,
                    "status": account.status
                },
                message="创建模拟账户成功"
            )
        else:
            return Response.error(message="暂不支持创建真实账户", code=400)
            
    except Exception as e:
        logger.error(f"创建账户失败: {e}")
        return Response.error(message=f"创建账户失败: {str(e)}")


@router.post("/switch", summary="切换当前账户")
async def switch_account(
    request: AccountSwitchRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """切换当前使用的账户"""
    try:
        # 验证账户是否存在
        result = await db.execute(
            select(Account).where(
                and_(
                    Account.account_id == request.account_id,
                    Account.user_id == current_user.id
                )
            )
        )
        account = result.scalar_one_or_none()
        
        if not account:
            return Response.error(message="账户不存在", code=404)
        
        if account.status != "ACTIVE":
            return Response.error(message="账户状态异常，无法切换", code=400)
        
        # 更新用户的当前账户设置（这里简化处理，实际应该保存到用户设置中）
        # 在实际应用中，可能需要在User模型中添加current_account_id字段
        # 或者在session中保存当前账户信息
        
        return Response.success(
            data={
                "account_id": account.account_id,
                "account_type": account.account_type.value,
                "message": f"已切换到{'模拟' if account.account_type == AccountType.SIMULATED else '真实'}账户"
            },
            message="切换账户成功"
        )
        
    except Exception as e:
        logger.error(f"切换账户失败: {e}")
        return Response.error(message=f"切换账户失败: {str(e)}")


@router.put("/update/{account_id}", summary="更新账户信息")
async def update_account(
    account_id: str,
    request: AccountUpdateRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """更新账户信息"""
    try:
        # 查询账户
        result = await db.execute(
            select(Account).where(
                and_(
                    Account.account_id == account_id,
                    Account.user_id == current_user.id
                )
            )
        )
        account = result.scalar_one_or_none()
        
        if not account:
            return Response.error(message="账户不存在", code=404)
        
        # 更新账户信息
        if request.account_name is not None:
            # 这里可以添加账户名称字段到模型中
            pass
        
        if request.settings is not None and account.account_type == AccountType.SIMULATED:
            account.simulated_settings.update(request.settings)
        
        account.update_time = datetime.now()
        await db.commit()
        
        return Response.success(message="更新账户信息成功")
        
    except Exception as e:
        logger.error(f"更新账户信息失败: {e}")
        return Response.error(message=f"更新账户信息失败: {str(e)}")


@router.put("/simulated/{account_id}/settings", summary="更新模拟账户设置")
async def update_simulated_settings(
    account_id: str,
    request: SimulatedSettingsUpdateRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """更新模拟账户的交易设置"""
    try:
        # 查询账户
        result = await db.execute(
            select(Account).where(
                and_(
                    Account.account_id == account_id,
                    Account.user_id == current_user.id,
                    Account.account_type == AccountType.SIMULATED
                )
            )
        )
        account = result.scalar_one_or_none()
        
        if not account:
            return Response.error(message="模拟账户不存在", code=404)
        
        # 更新设置
        settings = account.simulated_settings or {}
        update_data = request.dict(exclude_unset=True)
        settings.update(update_data)
        account.simulated_settings = settings
        
        await db.commit()
        
        return Response.success(
            data=account.simulated_settings,
            message="更新模拟账户设置成功"
        )
        
    except Exception as e:
        logger.error(f"更新模拟账户设置失败: {e}")
        return Response.error(message=f"更新模拟账户设置失败: {str(e)}")


@router.post("/simulated/{account_id}/reset", summary="重置模拟账户")
async def reset_simulated_account(
    account_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """重置模拟账户到初始状态"""
    try:
        # 查询账户
        result = await db.execute(
            select(Account).where(
                and_(
                    Account.account_id == account_id,
                    Account.user_id == current_user.id,
                    Account.account_type == AccountType.SIMULATED
                )
            )
        )
        account = result.scalar_one_or_none()
        
        if not account:
            return Response.error(message="模拟账户不存在", code=404)
        
        # 使用模拟交易引擎重置账户
        market_service = MockMarketService()
        engine = SimulatedTradingEngine(market_service)
        
        account = await engine.reset_account(db, account)
        
        return Response.success(
            data={
                "account_id": account.account_id,
                "total_assets": account.total_assets,
                "available_cash": account.available_cash,
                "reset_count": account.reset_count
            },
            message="重置模拟账户成功"
        )
        
    except Exception as e:
        logger.error(f"重置模拟账户失败: {e}")
        return Response.error(message=f"重置模拟账户失败: {str(e)}")


@router.get("/simulated/{account_id}/stats", summary="获取模拟账户统计")
async def get_simulated_stats(
    account_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    days: int = Query(30, ge=1, le=365, description="统计天数")
):
    """获取模拟账户的交易统计数据"""
    try:
        # 查询账户
        result = await db.execute(
            select(Account).where(
                and_(
                    Account.account_id == account_id,
                    Account.user_id == current_user.id,
                    Account.account_type == AccountType.SIMULATED
                )
            )
        )
        account = result.scalar_one_or_none()
        
        if not account:
            return Response.error(message="模拟账户不存在", code=404)
        
        # 获取统计数据
        start_date = datetime.now().date() - timedelta(days=days)
        stats_result = await db.execute(
            select(SimulatedAccountStats).where(
                and_(
                    SimulatedAccountStats.account_id == account.id,
                    SimulatedAccountStats.date >= start_date
                )
            ).order_by(SimulatedAccountStats.date)
        )
        stats_list = stats_result.scalars().all()
        
        # 格式化统计数据
        daily_stats = []
        for stat in stats_list:
            daily_stats.append({
                "date": stat.date.isoformat(),
                "daily_pnl": stat.daily_pnl,
                "daily_return": stat.daily_return,
                "cumulative_return": stat.cumulative_return,
                "total_trades": stat.total_trades,
                "winning_trades": stat.winning_trades,
                "win_rate": stat.win_rate,
                "total_commission": stat.total_commission,
                "max_drawdown": stat.max_drawdown,
                "sharpe_ratio": stat.sharpe_ratio
            })
        
        # 计算汇总统计
        if stats_list:
            total_trades = sum([s.total_trades for s in stats_list])
            total_winning = sum([s.winning_trades for s in stats_list])
            total_commission = sum([s.total_commission for s in stats_list])
            
            summary = {
                "total_trades": total_trades,
                "win_rate": total_winning / total_trades if total_trades > 0 else 0,
                "total_commission": total_commission,
                "cumulative_return": stats_list[-1].cumulative_return if stats_list else 0,
                "max_drawdown": max([s.max_drawdown for s in stats_list if s.max_drawdown], default=0),
                "best_day_return": max([s.daily_return for s in stats_list if s.daily_return], default=0),
                "worst_day_return": min([s.daily_return for s in stats_list if s.daily_return], default=0)
            }
        else:
            summary = {
                "total_trades": 0,
                "win_rate": 0,
                "total_commission": 0,
                "cumulative_return": 0,
                "max_drawdown": 0,
                "best_day_return": 0,
                "worst_day_return": 0
            }
        
        return Response.success(
            data={
                "summary": summary,
                "daily_stats": daily_stats
            },
            message="获取模拟账户统计成功"
        )
        
    except Exception as e:
        logger.error(f"获取模拟账户统计失败: {e}")
        return Response.error(message=f"获取模拟账户统计失败: {str(e)}")


@router.delete("/simulated/{account_id}", summary="删除模拟账户")
async def delete_simulated_account(
    account_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """删除模拟账户"""
    try:
        # 查询账户
        result = await db.execute(
            select(Account).where(
                and_(
                    Account.account_id == account_id,
                    Account.user_id == current_user.id,
                    Account.account_type == AccountType.SIMULATED
                )
            )
        )
        account = result.scalar_one_or_none()
        
        if not account:
            return Response.error(message="模拟账户不存在", code=404)
        
        # 检查是否有未完成订单
        pending_orders = await db.execute(
            select(func.count(Order.id)).where(
                and_(
                    Order.user_id == current_user.id,
                    Order.status.in_([
                        OrderStatus.SUBMITTING,
                        OrderStatus.SUBMITTED,
                        OrderStatus.NOTTRADED,
                        OrderStatus.PARTTRADED
                    ])
                )
            )
        )
        
        if pending_orders.scalar() > 0:
            return Response.error(message="存在未完成订单，无法删除账户", code=400)
        
        # 标记为删除状态而不是真正删除
        account.status = "DELETED"
        await db.commit()
        
        return Response.success(message="删除模拟账户成功")
        
    except Exception as e:
        logger.error(f"删除模拟账户失败: {e}")
        return Response.error(message=f"删除模拟账户失败: {str(e)}")