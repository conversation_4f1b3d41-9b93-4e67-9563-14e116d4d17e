"""
增强版回测API
整合回测引擎、数据分析和可视化功能
"""
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import uuid
import asyncio

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.dependencies_fixed import get_current_active_user
from app.db.models.user import User
from app.services.backtest_engine_enhanced import BacktestEngine
from app.services.backtest_data_analyzer import BacktestDataAnalyzer
from app.services.backtest_visualizer import BacktestVisualizer

router = APIRouter(tags=["回测增强版"])

# 全局实例
backtest_engine = BacktestEngine()
data_analyzer = BacktestDataAnalyzer()
visualizer = BacktestVisualizer()

# 内存存储回测结果（生产环境应使用数据库）
backtest_results_store = {}


@router.post("/enhanced/run")
async def run_enhanced_backtest(
    strategy_code: str,
    symbols: List[str],
    start_date: datetime,
    end_date: datetime,
    initial_capital: float = 100000.0,
    benchmark: str = "000300.SH",
    parameters: Optional[Dict[str, Any]] = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    运行增强版回测
    
    - **strategy_code**: 策略代码
    - **symbols**: 交易标的列表  
    - **start_date**: 开始日期
    - **end_date**: 结束日期
    - **initial_capital**: 初始资金
    - **benchmark**: 基准指标
    - **parameters**: 策略参数
    """
    try:
        # 参数验证
        if start_date >= end_date:
            raise HTTPException(status_code=400, detail="开始日期必须早于结束日期")
        
        if initial_capital <= 0:
            raise HTTPException(status_code=400, detail="初始资金必须大于0")
        
        if not symbols:
            raise HTTPException(status_code=400, detail="至少需要一个交易标的")
        
        # 生成回测ID
        backtest_id = f"BT{datetime.now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:8].upper()}"
        
        # 运行回测
        backtest_result = await backtest_engine.run_backtest(
            strategy_code=strategy_code,
            symbols=symbols,
            start_date=start_date,
            end_date=end_date,
            initial_capital=initial_capital,
            benchmark=benchmark,
            parameters=parameters or {}
        )
        
        # 存储结果
        backtest_results_store[backtest_id] = {
            "id": backtest_id,
            "user_id": current_user.id,
            "result": backtest_result,
            "created_at": datetime.now(),
            "status": "completed"
        }
        
        return {
            "success": True,
            "message": "回测完成",
            "backtest_id": backtest_id,
            "summary": backtest_result["summary"],
            "performance": backtest_result["performance"]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"回测失败: {str(e)}")


@router.get("/enhanced/{backtest_id}/result")
async def get_backtest_result(
    backtest_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """获取回测结果"""
    if backtest_id not in backtest_results_store:
        raise HTTPException(status_code=404, detail="回测结果不存在")
    
    stored_result = backtest_results_store[backtest_id]
    
    # 验证权限
    if stored_result["user_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此回测结果")
    
    return {
        "success": True,
        "data": stored_result["result"]
    }


@router.get("/enhanced/{backtest_id}/analysis")
async def get_backtest_analysis(
    backtest_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """获取回测深度分析"""
    if backtest_id not in backtest_results_store:
        raise HTTPException(status_code=404, detail="回测结果不存在")
    
    stored_result = backtest_results_store[backtest_id]
    
    # 验证权限
    if stored_result["user_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此回测结果")
    
    try:
        # 执行深度分析
        analysis_result = await data_analyzer.analyze_backtest_results(
            stored_result["result"]
        )
        
        return {
            "success": True,
            "data": analysis_result
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")


@router.get("/enhanced/{backtest_id}/visualization")
async def get_backtest_visualization(
    backtest_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """获取回测可视化数据"""
    if backtest_id not in backtest_results_store:
        raise HTTPException(status_code=404, detail="回测结果不存在")
    
    stored_result = backtest_results_store[backtest_id]
    
    # 验证权限
    if stored_result["user_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此回测结果")
    
    try:
        # 先进行分析
        analysis_result = await data_analyzer.analyze_backtest_results(
            stored_result["result"]
        )
        
        # 生成可视化数据
        visualization_data = await visualizer.generate_visualization_data(
            stored_result["result"],
            analysis_result
        )
        
        return {
            "success": True,
            "data": visualization_data
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"可视化生成失败: {str(e)}")


@router.get("/enhanced/{backtest_id}/report")
async def get_comprehensive_report(
    backtest_id: str,
    format: str = Query("json", regex="^(json|pdf)$"),
    current_user: User = Depends(get_current_active_user)
):
    """获取综合回测报告"""
    if backtest_id not in backtest_results_store:
        raise HTTPException(status_code=404, detail="回测结果不存在")
    
    stored_result = backtest_results_store[backtest_id]
    
    # 验证权限
    if stored_result["user_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此回测结果")
    
    try:
        # 获取完整分析
        analysis_result = await data_analyzer.analyze_backtest_results(
            stored_result["result"]
        )
        
        # 获取可视化数据
        visualization_data = await visualizer.generate_visualization_data(
            stored_result["result"],
            analysis_result
        )
        
        # 生成综合报告
        comprehensive_report = {
            "backtest_id": backtest_id,
            "generated_at": datetime.now().isoformat(),
            "basic_results": stored_result["result"],
            "analysis": analysis_result,
            "visualization": visualization_data,
            "summary": {
                "strategy_performance": "优秀" if stored_result["result"]["summary"]["total_return"] > 10 else "良好",
                "risk_level": "中等",
                "recommendation": "策略表现良好，建议继续优化参数"
            }
        }
        
        if format == "pdf":
            # 生成PDF报告
            pdf_content = await visualizer.generate_report_pdf(visualization_data)
            return {
                "success": True,
                "format": "pdf",
                "content": pdf_content.decode('utf-8', errors='ignore')
            }
        else:
            return {
                "success": True,
                "format": "json",
                "data": comprehensive_report
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"报告生成失败: {str(e)}")


@router.get("/enhanced/list")
async def list_user_backtests(
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    current_user: User = Depends(get_current_active_user)
):
    """获取用户回测列表"""
    user_backtests = [
        {
            "backtest_id": bt_id,
            "created_at": data["created_at"].isoformat(),
            "status": data["status"],
            "summary": data["result"]["summary"]
        }
        for bt_id, data in backtest_results_store.items()
        if data["user_id"] == current_user.id
    ]
    
    # 按创建时间排序
    user_backtests.sort(key=lambda x: x["created_at"], reverse=True)
    
    # 分页
    total = len(user_backtests)
    paginated_results = user_backtests[skip:skip + limit]
    
    return {
        "success": True,
        "data": paginated_results,
        "total": total,
        "skip": skip,
        "limit": limit
    }


@router.delete("/enhanced/{backtest_id}")
async def delete_backtest(
    backtest_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """删除回测结果"""
    if backtest_id not in backtest_results_store:
        raise HTTPException(status_code=404, detail="回测结果不存在")
    
    stored_result = backtest_results_store[backtest_id]
    
    # 验证权限
    if stored_result["user_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="无权删除此回测结果")
    
    # 删除结果
    del backtest_results_store[backtest_id]
    
    return {
        "success": True,
        "message": "回测结果已删除"
    }


@router.post("/enhanced/{backtest_id}/compare")
async def compare_backtests(
    backtest_id: str,
    compare_with: List[str],
    current_user: User = Depends(get_current_active_user)
):
    """对比多个回测结果"""
    # 验证所有回测ID
    all_ids = [backtest_id] + compare_with
    
    for bt_id in all_ids:
        if bt_id not in backtest_results_store:
            raise HTTPException(status_code=404, detail=f"回测结果 {bt_id} 不存在")
        
        stored_result = backtest_results_store[bt_id]
        if stored_result["user_id"] != current_user.id:
            raise HTTPException(status_code=403, detail=f"无权访问回测结果 {bt_id}")
    
    try:
        # 收集对比数据
        comparison_data = {}
        
        for bt_id in all_ids:
            result = backtest_results_store[bt_id]["result"]
            comparison_data[bt_id] = {
                "summary": result["summary"],
                "performance": result["performance"]
            }
        
        # 生成对比分析
        comparison_analysis = {
            "backtests": comparison_data,
            "best_performer": max(all_ids, key=lambda x: comparison_data[x]["summary"]["total_return"]),
            "most_stable": min(all_ids, key=lambda x: comparison_data[x]["summary"]["volatility"]),
            "best_sharpe": max(all_ids, key=lambda x: comparison_data[x]["summary"]["sharpe_ratio"]),
            "comparison_table": []
        }
        
        # 创建对比表
        metrics = ["total_return", "annual_return", "volatility", "sharpe_ratio", "max_drawdown", "win_rate"]
        
        for metric in metrics:
            row = {"metric": metric, "values": {}}
            for bt_id in all_ids:
                row["values"][bt_id] = comparison_data[bt_id]["summary"].get(metric, 0)
            comparison_analysis["comparison_table"].append(row)
        
        return {
            "success": True,
            "data": comparison_analysis
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"对比分析失败: {str(e)}")


@router.get("/enhanced/demo")
async def run_demo_backtest():
    """运行演示回测（无需认证）"""
    try:
        # 演示参数
        demo_strategy = """
# 简单双移动平均线策略
def strategy_logic(data):
    # 计算移动平均线
    short_ma = data['close'].rolling(5).mean()
    long_ma = data['close'].rolling(20).mean()
    
    # 生成信号
    if short_ma.iloc[-1] > long_ma.iloc[-1]:
        return 'BUY'
    elif short_ma.iloc[-1] < long_ma.iloc[-1]:
        return 'SELL'
    else:
        return 'HOLD'
"""
        
        # 运行演示回测
        demo_result = await backtest_engine.run_backtest(
            strategy_code=demo_strategy,
            symbols=["000001.SZ", "000002.SZ"],
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 6, 30),
            initial_capital=100000.0,
            parameters={"ma_short": 5, "ma_long": 20}
        )
        
        return {
            "success": True,
            "message": "演示回测完成",
            "data": demo_result
        }
        
    except Exception as e:
        return {
            "success": False,
            "message": f"演示回测失败: {str(e)}"
        }


@router.get("/enhanced/health")
async def health_check():
    """回测系统健康检查"""
    return {
        "status": "healthy",
        "module": "backtest_enhanced",
        "timestamp": datetime.now().isoformat(),
        "active_backtests": len(backtest_results_store),
        "engine_status": "ready",
        "analyzer_status": "ready",
        "visualizer_status": "ready"
    }