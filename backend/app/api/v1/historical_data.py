"""
历史数据API路由
处理历史行情数据的查询和导出
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.auth import get_current_user
from app.db.models.user import User
from app.services.historical_data_service import historical_data_service
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/stats", summary="获取历史数据统计信息")
async def get_historical_stats(
    current_user: User = Depends(get_current_user)
):
    """获取历史数据统计信息"""
    try:
        stats = await historical_data_service.get_stats()
        return stats
    except Exception as e:
        logger.error(f"获取历史数据统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取统计信息失败")


@router.get("/stocks", summary="获取股票列表")
async def get_stock_list(
    keyword: Optional[str] = Query(None, description="搜索关键词"),
    market: Optional[str] = Query(None, description="交易所：SH/SZ/BJ"),
    industry: Optional[str] = Query(None, description="行业"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(50, ge=1, le=200, description="每页数量"),
    current_user: User = Depends(get_current_user)
):
    """获取股票列表"""
    try:
        result = await historical_data_service.get_stock_list(
            keyword=keyword,
            market=market,
            industry=industry,
            page=page,
            page_size=page_size
        )
        return result
    except Exception as e:
        logger.error(f"获取股票列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取股票列表失败")


@router.get("/stocks/{symbol}/data", summary="获取股票历史数据")
async def get_stock_data(
    symbol: str,
    start_date: Optional[str] = Query(None, description="开始日期，格式：YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式：YYYY-MM-DD"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(100, ge=1, le=1000, description="每页数量"),
    current_user: User = Depends(get_current_user)
):
    """获取指定股票的历史数据"""
    try:
        result = await historical_data_service.get_stock_data(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            page=page,
            page_size=page_size
        )
        if not result:
            raise HTTPException(status_code=404, detail=f"未找到股票 {symbol} 的历史数据")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取股票 {symbol} 历史数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取历史数据失败")


@router.get("/stocks/{symbol}/latest", summary="获取股票最新数据")
async def get_stock_latest_data(
    symbol: str,
    current_user: User = Depends(get_current_user)
):
    """获取指定股票的最新数据"""
    try:
        result = await historical_data_service.get_latest_data(symbol)
        if not result:
            raise HTTPException(status_code=404, detail=f"未找到股票 {symbol} 的数据")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取股票 {symbol} 最新数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取最新数据失败")


@router.get("/industries", summary="获取行业列表")
async def get_industries(
    current_user: User = Depends(get_current_user)
):
    """获取所有行业列表"""
    try:
        industries = await historical_data_service.get_industries()
        return {"industries": industries}
    except Exception as e:
        logger.error(f"获取行业列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取行业列表失败")


@router.get("/hot-stocks", summary="获取热门股票")
async def get_hot_stocks(
    category: Optional[str] = Query(None, description="分类：热门股票/银行股/科技股/白酒股"),
    limit: int = Query(20, ge=1, le=100, description="返回数量"),
    current_user: User = Depends(get_current_user)
):
    """获取热门股票列表"""
    try:
        stocks = await historical_data_service.get_hot_stocks(category=category, limit=limit)
        return {"stocks": stocks}
    except Exception as e:
        logger.error(f"获取热门股票失败: {e}")
        raise HTTPException(status_code=500, detail="获取热门股票失败")


@router.post("/export", summary="导出股票数据")
async def export_stock_data(
    symbols: List[str],
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    format: str = Query("csv", description="导出格式：csv/excel"),
    current_user: User = Depends(get_current_user)
):
    """导出多只股票的历史数据"""
    try:
        if not symbols:
            raise HTTPException(status_code=400, detail="请选择要导出的股票")
        
        if len(symbols) > 100:
            raise HTTPException(status_code=400, detail="一次最多导出100只股票")
        
        result = await historical_data_service.export_data(
            symbols=symbols,
            start_date=start_date,
            end_date=end_date,
            format=format
        )
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出股票数据失败: {e}")
        raise HTTPException(status_code=500, detail="导出数据失败")