"""
Custom exceptions for the quantitative trading platform.
Enhanced with error codes, severity levels, and recovery hints.
"""

from typing import Any, Dict, Optional, List
from enum import Enum
import traceback
import uuid
from datetime import datetime


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories for better classification."""
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    VALIDATION = "validation"
    BUSINESS_LOGIC = "business_logic"
    DATA_ACCESS = "data_access"
    NETWORK = "network"
    SYSTEM = "system"
    EXTERNAL_SERVICE = "external_service"
    CONFIGURATION = "configuration"
    RATE_LIMITING = "rate_limiting"
    RESOURCE_NOT_FOUND = "resource_not_found"
    CONFLICT = "conflict"
    MARKET_DATA = "market_data"
    TRADING = "trading"
    STRATEGY = "strategy"
    BACKTEST = "backtest"
    RISK_MANAGEMENT = "risk_management"
    CTP = "ctp"


class BaseCustomException(Exception):
    """
    Enhanced base exception class for all custom exceptions.
    
    Features:
    - Error codes and categories
    - Severity levels
    - Recovery hints
    - Context data
    - Error tracking
    """

    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        category: Optional[ErrorCategory] = None,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        details: Optional[Dict[str, Any]] = None,
        context: Optional[Dict[str, Any]] = None,
        recovery_hint: Optional[str] = None,
        user_message: Optional[str] = None,
        should_retry: bool = False,
        retry_after: Optional[int] = None,
        causes: Optional[List[Exception]] = None
    ):
        self.message = message
        self.user_message = user_message or message
        self.error_code = error_code or self._generate_error_code()
        self.category = category or self._get_default_category()
        self.severity = severity
        self.details = details or {}
        self.context = context or {}
        self.recovery_hint = recovery_hint
        self.should_retry = should_retry
        self.retry_after = retry_after  # seconds
        self.causes = causes or []
        
        # Tracking information
        self.error_id = str(uuid.uuid4())
        self.timestamp = datetime.utcnow()
        self.traceback_str = traceback.format_exc()
        
        super().__init__(self.message)
    
    def _generate_error_code(self) -> str:
        """Generate a default error code based on class name."""
        class_name = self.__class__.__name__
        # Convert CamelCase to SNAKE_CASE
        import re
        code = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', class_name)
        code = re.sub('([a-z0-9])([A-Z])', r'\1_\2', code).upper()
        # Remove _ERROR suffix if present
        if code.endswith('_ERROR'):
            code = code[:-6]
        return code
    
    def _get_default_category(self) -> ErrorCategory:
        """Get default category based on exception type."""
        return ErrorCategory.SYSTEM
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for logging/serialization."""
        return {
            "error_id": self.error_id,
            "error_code": self.error_code,
            "category": self.category.value if self.category else None,
            "severity": self.severity.value,
            "message": self.message,
            "user_message": self.user_message,
            "details": self.details,
            "context": self.context,
            "recovery_hint": self.recovery_hint,
            "should_retry": self.should_retry,
            "retry_after": self.retry_after,
            "timestamp": self.timestamp.isoformat(),
            "traceback": self.traceback_str if self.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL] else None,
            "causes": [str(cause) for cause in self.causes]
        }
    
    def add_context(self, key: str, value: Any) -> None:
        """Add context information to the exception."""
        self.context[key] = value
    
    def add_cause(self, cause: Exception) -> None:
        """Add a causing exception."""
        self.causes.append(cause)
    
    def is_retryable(self) -> bool:
        """Check if the error is retryable."""
        return self.should_retry
    
    def get_retry_delay(self) -> Optional[int]:
        """Get the recommended retry delay in seconds."""
        return self.retry_after


class DataNotFoundError(BaseCustomException):
    """Raised when requested data is not found."""
    
    def __init__(self, message: str = "请求的数据未找到", **kwargs):
        super().__init__(
            message=message,
            error_code="DATA_NOT_FOUND",
            category=ErrorCategory.RESOURCE_NOT_FOUND,
            severity=ErrorSeverity.LOW,
            user_message="请求的数据不存在",
            recovery_hint="请检查请求参数或联系管理员",
            **kwargs
        )


class ValidationError(BaseCustomException):
    """Raised when data validation fails."""
    
    def __init__(self, message: str = "数据验证失败", **kwargs):
        super().__init__(
            message=message,
            error_code="VALIDATION_FAILED",
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.LOW,
            user_message="输入数据格式不正确",
            recovery_hint="请检查输入数据的格式和完整性",
            **kwargs
        )


class AuthenticationError(BaseCustomException):
    """Raised when authentication fails."""
    
    def __init__(self, message: str = "身份验证失败", **kwargs):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_FAILED",
            category=ErrorCategory.AUTHENTICATION,
            severity=ErrorSeverity.MEDIUM,
            user_message="身份验证失败，请重新登录",
            recovery_hint="请使用正确的用户名和密码重新登录",
            **kwargs
        )


class AuthorizationError(BaseCustomException):
    """Raised when authorization fails."""
    
    def __init__(self, message: str = "权限不足", **kwargs):
        super().__init__(
            message=message,
            error_code="AUTHORIZATION_DENIED",
            category=ErrorCategory.AUTHORIZATION,
            severity=ErrorSeverity.MEDIUM,
            user_message="您没有执行此操作的权限",
            recovery_hint="请联系管理员获取相应权限",
            **kwargs
        )


class ConfigurationError(BaseCustomException):
    """Raised when configuration is invalid."""
    
    def __init__(self, message: str = "配置错误", **kwargs):
        super().__init__(
            message=message,
            error_code="CONFIGURATION_ERROR",
            category=ErrorCategory.CONFIGURATION,
            severity=ErrorSeverity.HIGH,
            user_message="系统配置异常",
            recovery_hint="请联系技术支持人员",
            **kwargs
        )


class DatabaseError(BaseCustomException):
    """Raised when database operations fail."""
    
    def __init__(self, message: str = "数据库操作失败", **kwargs):
        super().__init__(
            message=message,
            error_code="DATABASE_ERROR",
            category=ErrorCategory.DATA_ACCESS,
            severity=ErrorSeverity.HIGH,
            user_message="数据库服务暂时不可用",
            recovery_hint="请稍后重试，如问题持续请联系技术支持",
            should_retry=True,
            retry_after=30,
            **kwargs
        )


class NetworkError(BaseCustomException):
    """Raised when network operations fail."""
    
    def __init__(self, message: str = "网络连接失败", **kwargs):
        super().__init__(
            message=message,
            error_code="NETWORK_ERROR",
            category=ErrorCategory.NETWORK,
            severity=ErrorSeverity.MEDIUM,
            user_message="网络连接异常",
            recovery_hint="请检查网络连接后重试",
            should_retry=True,
            retry_after=60,
            **kwargs
        )


class MarketDataError(BaseCustomException):
    """Raised when market data operations fail."""
    
    def __init__(self, message: str = "行情数据服务异常", **kwargs):
        super().__init__(
            message=message,
            error_code="MARKET_DATA_ERROR",
            category=ErrorCategory.MARKET_DATA,
            severity=ErrorSeverity.MEDIUM,
            user_message="行情数据暂时不可用",
            recovery_hint="行情服务可能正在维护，请稍后重试",
            should_retry=True,
            retry_after=120,
            **kwargs
        )


class TradingError(BaseCustomException):
    """Raised when trading operations fail."""
    
    def __init__(self, message: str = "交易操作失败", **kwargs):
        super().__init__(
            message=message,
            error_code="TRADING_ERROR",
            category=ErrorCategory.TRADING,
            severity=ErrorSeverity.HIGH,
            user_message="交易操作失败",
            recovery_hint="请检查交易参数或联系客服",
            **kwargs
        )


class StrategyError(BaseCustomException):
    """Raised when strategy operations fail."""
    
    def __init__(self, message: str = "策略执行异常", **kwargs):
        super().__init__(
            message=message,
            error_code="STRATEGY_ERROR",
            category=ErrorCategory.STRATEGY,
            severity=ErrorSeverity.MEDIUM,
            user_message="策略执行出现异常",
            recovery_hint="请检查策略参数和逻辑",
            **kwargs
        )


class BacktestError(BaseCustomException):
    """Raised when backtest operations fail."""
    
    def __init__(self, message: str = "回测执行失败", **kwargs):
        super().__init__(
            message=message,
            error_code="BACKTEST_ERROR",
            category=ErrorCategory.BACKTEST,
            severity=ErrorSeverity.MEDIUM,
            user_message="回测任务执行失败",
            recovery_hint="请检查回测参数和数据完整性",
            **kwargs
        )


class RiskManagementError(BaseCustomException):
    """Raised when risk management checks fail."""
    
    def __init__(self, message: str = "风控检查失败", **kwargs):
        super().__init__(
            message=message,
            error_code="RISK_CONTROL_VIOLATION",
            category=ErrorCategory.RISK_MANAGEMENT,
            severity=ErrorSeverity.HIGH,
            user_message="操作被风控系统拒绝",
            recovery_hint="请调整操作参数或联系风控管理员",
            **kwargs
        )


class CTPError(BaseCustomException):
    """Raised when CTP operations fail."""
    
    def __init__(self, message: str = "CTP服务异常", **kwargs):
        super().__init__(
            message=message,
            error_code="CTP_ERROR",
            category=ErrorCategory.CTP,
            severity=ErrorSeverity.HIGH,
            user_message="期货交易服务暂时不可用",
            recovery_hint="CTP服务可能正在维护，请稍后重试",
            should_retry=True,
            retry_after=300,
            **kwargs
        )


class ServiceUnavailableError(BaseCustomException):
    """Raised when a service is temporarily unavailable."""
    
    def __init__(self, message: str = "服务暂时不可用", **kwargs):
        super().__init__(
            message=message,
            error_code="SERVICE_UNAVAILABLE",
            category=ErrorCategory.SYSTEM,
            severity=ErrorSeverity.MEDIUM,
            user_message="服务暂时不可用",
            recovery_hint="服务可能正在维护，请稍后重试",
            should_retry=True,
            retry_after=180,
            **kwargs
        )


class RateLimitError(BaseCustomException):
    """Raised when rate limits are exceeded."""
    
    def __init__(self, message: str = "请求频率超限", **kwargs):
        super().__init__(
            message=message,
            error_code="RATE_LIMIT_EXCEEDED",
            category=ErrorCategory.RATE_LIMITING,
            severity=ErrorSeverity.LOW,
            user_message="请求过于频繁，请稍后重试",
            recovery_hint="请降低请求频率",
            should_retry=True,
            retry_after=60,
            **kwargs
        )


class InternalServerError(BaseCustomException):
    """Raised when internal server errors occur."""
    
    def __init__(self, message: str = "服务器内部错误", **kwargs):
        super().__init__(
            message=message,
            error_code="INTERNAL_SERVER_ERROR",
            category=ErrorCategory.SYSTEM,
            severity=ErrorSeverity.CRITICAL,
            user_message="服务器内部错误",
            recovery_hint="请稍后重试，如问题持续请联系技术支持",
            should_retry=True,
            retry_after=120,
            **kwargs
        )


class BusinessLogicError(BaseCustomException):
    """Raised when business logic validation fails."""
    
    def __init__(self, message: str = "业务逻辑验证失败", **kwargs):
        super().__init__(
            message=message,
            error_code="BUSINESS_LOGIC_ERROR",
            category=ErrorCategory.BUSINESS_LOGIC,
            severity=ErrorSeverity.MEDIUM,
            user_message="操作违反业务规则",
            recovery_hint="请检查操作参数是否符合业务要求",
            **kwargs
        )


class ExternalServiceError(BaseCustomException):
    """Raised when external service calls fail."""
    
    def __init__(self, message: str = "外部服务调用失败", **kwargs):
        super().__init__(
            message=message,
            error_code="EXTERNAL_SERVICE_ERROR",
            category=ErrorCategory.EXTERNAL_SERVICE,
            severity=ErrorSeverity.MEDIUM,
            user_message="外部服务暂时不可用",
            recovery_hint="请稍后重试，如问题持续请联系技术支持",
            should_retry=True,
            retry_after=180,
            **kwargs
        )


class ResourceNotFoundError(BaseCustomException):
    """Raised when a requested resource is not found."""
    
    def __init__(self, message: str = "请求的资源未找到", **kwargs):
        super().__init__(
            message=message,
            error_code="RESOURCE_NOT_FOUND",
            category=ErrorCategory.RESOURCE_NOT_FOUND,
            severity=ErrorSeverity.LOW,
            user_message="请求的资源不存在",
            recovery_hint="请检查资源路径或参数",
            **kwargs
        )


class ConflictError(BaseCustomException):
    """Raised when a resource conflict occurs."""

    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.CONFLICT


class TimeoutError(BaseCustomException):
    """Raised when operations timeout."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.SYSTEM


class CircuitBreakerError(BaseCustomException):
    """Raised when circuit breaker is open."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.SYSTEM


class QuotaExceededError(BaseCustomException):
    """Raised when quota/limit is exceeded."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.RATE_LIMITING


class DataIntegrityError(BaseCustomException):
    """Raised when data integrity is compromised."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.DATA_ACCESS


class ThrottlingError(BaseCustomException):
    """Raised when request is throttled."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.RATE_LIMITING


class MaintenanceError(BaseCustomException):
    """Raised when system is under maintenance."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.SYSTEM


class DeprecationError(BaseCustomException):
    """Raised when deprecated API is used."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.BUSINESS_LOGIC


class ComplianceError(BaseCustomException):
    """Raised when compliance rules are violated."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.BUSINESS_LOGIC


class IntegrationError(BaseCustomException):
    """Raised when third-party integration fails."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.EXTERNAL_SERVICE


class WebSocketError(BaseCustomException):
    """Raised when WebSocket operations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.NETWORK


class CacheError(BaseCustomException):
    """Raised when cache operations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.SYSTEM


class QueueError(BaseCustomException):
    """Raised when queue operations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.SYSTEM


class DataValidationError(BaseCustomException):
    """Raised when data validation fails."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.VALIDATION


class APIVersionError(BaseCustomException):
    """Raised when API version is not supported."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.BUSINESS_LOGIC


class FeatureFlagError(BaseCustomException):
    """Raised when feature flag operations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.CONFIGURATION


class HealthCheckError(BaseCustomException):
    """Raised when health check fails."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.SYSTEM


class MetricsError(BaseCustomException):
    """Raised when metrics collection fails."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.SYSTEM


class AuditError(BaseCustomException):
    """Raised when audit logging fails."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.SYSTEM


class EventError(BaseCustomException):
    """Raised when event processing fails."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.SYSTEM


class EncryptionError(BaseCustomException):
    """Raised when encryption/decryption fails."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.SYSTEM


class TokenError(BaseCustomException):
    """Raised when token operations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.AUTHENTICATION


class SessionError(BaseCustomException):
    """Raised when session operations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.AUTHENTICATION


# 特定于量化平台的异常
class MarketClosedError(BaseCustomException):
    """Raised when market is closed for trading."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.MARKET_DATA


class InsufficientFundsError(BaseCustomException):
    """Raised when insufficient funds for trading."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.TRADING


class PositionLimitError(BaseCustomException):
    """Raised when position limit is exceeded."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.RISK_MANAGEMENT


class OrderValidationError(BaseCustomException):
    """Raised when order validation fails."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.TRADING


class StrategyExecutionError(BaseCustomException):
    """Raised when strategy execution fails."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.STRATEGY


class BacktestError(BaseCustomException):
    """Raised when backtest operations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.BACKTEST


class DataFeedError(BaseCustomException):
    """Raised when data feed operations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.MARKET_DATA


class RiskLimitError(BaseCustomException):
    """Raised when risk limits are breached."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.RISK_MANAGEMENT


class CTPConnectionError(BaseCustomException):
    """Raised when CTP connection fails."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.CTP


class CTPAuthError(BaseCustomException):
    """Raised when CTP authentication fails."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.CTP


class CTPOrderError(BaseCustomException):
    """Raised when CTP order operations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.CTP


class CTPDataError(BaseCustomException):
    """Raised when CTP data operations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.CTP


class PortfolioError(BaseCustomException):
    """Raised when portfolio operations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.TRADING


class PriceError(BaseCustomException):
    """Raised when price-related operations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.MARKET_DATA


class IndicatorError(BaseCustomException):
    """Raised when technical indicator calculations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.STRATEGY


class SignalError(BaseCustomException):
    """Raised when trading signal generation fails."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.STRATEGY


class AlertError(BaseCustomException):
    """Raised when alert operations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.SYSTEM


class NotificationError(BaseCustomException):
    """Raised when notification delivery fails."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.SYSTEM


class ReportError(BaseCustomException):
    """Raised when report generation fails."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.SYSTEM


class AnalyticsError(BaseCustomException):
    """Raised when analytics operations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.SYSTEM


class UserPreferenceError(BaseCustomException):
    """Raised when user preference operations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.BUSINESS_LOGIC


class WatchlistError(BaseCustomException):
    """Raised when watchlist operations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.BUSINESS_LOGIC


class CalendarError(BaseCustomException):
    """Raised when calendar operations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.MARKET_DATA


class HolidayError(BaseCustomException):
    """Raised when holiday calendar operations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.MARKET_DATA


class BenchmarkError(BaseCustomException):
    """Raised when benchmark operations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.STRATEGY


class OptimizationError(BaseCustomException):
    """Raised when optimization operations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.STRATEGY


class SimulationError(BaseCustomException):
    """Raised when simulation operations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.BACKTEST


class DataQualityError(BaseCustomException):
    """Raised when data quality issues are detected."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.DATA_ACCESS


class LiquidityError(BaseCustomException):
    """Raised when liquidity issues occur."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.TRADING


class SlippageError(BaseCustomException):
    """Raised when slippage exceeds limits."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.TRADING


class CommissionError(BaseCustomException):
    """Raised when commission calculation fails."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.TRADING


class MarginError(BaseCustomException):
    """Raised when margin requirements are not met."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.TRADING


class VolatilityError(BaseCustomException):
    """Raised when volatility calculations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.STRATEGY


class CorrelationError(BaseCustomException):
    """Raised when correlation calculations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.STRATEGY


class DrawdownError(BaseCustomException):
    """Raised when drawdown limits are exceeded."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.RISK_MANAGEMENT


class SharpeRatioError(BaseCustomException):
    """Raised when Sharpe ratio calculations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.STRATEGY


class VaRError(BaseCustomException):
    """Raised when Value at Risk calculations fail."""
    
    def _get_default_category(self) -> ErrorCategory:
        return ErrorCategory.RISK_MANAGEMENT


# 别名（向后兼容）
BusinessException = BusinessLogicError
