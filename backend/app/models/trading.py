"""
交易相关数据库模型
"""
from sqlalchemy import Column, String, Float, Integer, DateTime, Boolean, ForeignKey, Enum, Index, JSON, Date
from sqlalchemy.orm import relationship
from datetime import datetime
import enum

from app.core.database import Base


class OrderDirection(str, enum.Enum):
    """订单方向"""
    BUY = "BUY"
    SELL = "SELL"


class OrderType(str, enum.Enum):
    """订单类型"""
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP = "STOP"
    STOP_LIMIT = "STOP_LIMIT"


class OrderStatus(str, enum.Enum):
    """订单状态"""
    SUBMITTING = "SUBMITTING"
    SUBMITTED = "SUBMITTED"
    NOTTRADED = "NOTTRADED"
    PARTTRADED = "PARTTRADED"
    ALL_TRADED = "ALL_TRADED"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"


class AccountType(str, enum.Enum):
    """账户类型"""
    REAL = "REAL"
    SIMULATED = "SIMULATED"


class SlippageMode(str, enum.Enum):
    """滑点模式"""
    FIXED = "FIXED"
    RANDOM = "RANDOM"
    MARKET_IMPACT = "MARKET_IMPACT"


class Order(Base):
    """订单模型"""
    __tablename__ = "orders"
    
    id = Column(Integer, primary_key=True, index=True)
    order_id = Column(String(64), unique=True, nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 交易信息
    symbol = Column(String(20), nullable=False, index=True)
    exchange = Column(String(10), nullable=False)
    direction = Column(Enum(OrderDirection), nullable=False)
    order_type = Column(Enum(OrderType), nullable=False)
    
    # 价格和数量
    price = Column(Float, nullable=False)
    volume = Column(Integer, nullable=False)
    traded_volume = Column(Integer, default=0)
    avg_price = Column(Float, default=0)
    
    # 状态和时间
    status = Column(Enum(OrderStatus), nullable=False, index=True)
    submit_time = Column(DateTime, default=datetime.utcnow)
    update_time = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 其他信息
    commission = Column(Float, default=0)
    message = Column(String(255))
    
    # 索引
    __table_args__ = (
        Index('idx_user_symbol', 'user_id', 'symbol'),
        Index('idx_user_status', 'user_id', 'status'),
        Index('idx_submit_time', 'submit_time'),
    )
    
    # 关系
    user = relationship("User", back_populates="orders")
    trades = relationship("Trade", back_populates="order", cascade="all, delete-orphan")


class Trade(Base):
    """成交记录模型"""
    __tablename__ = "trades"
    
    id = Column(Integer, primary_key=True, index=True)
    trade_id = Column(String(64), unique=True, nullable=False, index=True)
    order_id = Column(String(64), ForeignKey("orders.order_id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 成交信息
    symbol = Column(String(20), nullable=False, index=True)
    exchange = Column(String(10), nullable=False)
    direction = Column(Enum(OrderDirection), nullable=False)
    
    # 成交详情
    price = Column(Float, nullable=False)
    volume = Column(Integer, nullable=False)
    commission = Column(Float, default=0)
    trade_time = Column(DateTime, default=datetime.utcnow, index=True)
    
    # 关系
    order = relationship("Order", back_populates="trades")
    user = relationship("User", back_populates="trades")


class Position(Base):
    """持仓模型"""
    __tablename__ = "positions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 持仓信息
    symbol = Column(String(20), nullable=False)
    exchange = Column(String(10), nullable=False)
    name = Column(String(50))  # 股票名称
    
    # 持仓数量
    volume = Column(Integer, nullable=False)
    available_volume = Column(Integer, nullable=False)
    frozen_volume = Column(Integer, default=0)
    
    # 成本和盈亏
    avg_price = Column(Float, nullable=False)
    current_price = Column(Float)
    market_value = Column(Float)
    profit_loss = Column(Float, default=0)
    profit_rate = Column(Float, default=0)
    
    # 今日盈亏
    day_profit_loss = Column(Float, default=0)
    day_profit_rate = Column(Float, default=0)
    
    # 时间
    create_time = Column(DateTime, default=datetime.utcnow)
    update_time = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 唯一约束和索引
    __table_args__ = (
        Index('idx_user_symbol_unique', 'user_id', 'symbol', unique=True),
    )
    
    # 关系
    user = relationship("User", back_populates="positions")


class Account(Base):
    """账户模型"""
    __tablename__ = "accounts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    account_id = Column(String(32), unique=True, nullable=False)
    
    # 账户类型
    account_type = Column(Enum(AccountType), default=AccountType.REAL)
    parent_account_id = Column(Integer, ForeignKey('accounts.id'), nullable=True)
    
    # 资金信息
    total_assets = Column(Float, default=0)
    available_cash = Column(Float, default=0)
    frozen_cash = Column(Float, default=0)
    market_value = Column(Float, default=0)
    
    # 模拟账户特有字段
    initial_capital = Column(Float, default=1000000.0)
    reset_count = Column(Integer, default=0)
    created_reason = Column(String(50))  # 'USER_CREATE', 'STRATEGY_TEST', 'EDUCATION'
    
    # 模拟账户设置
    simulated_settings = Column(JSON, default={
        "allow_short": False,
        "allow_margin": False,
        "slippage_mode": "RANDOM",
        "execution_delay": 300,
        "partial_fill": True,
        "market_impact_factor": 0.0001
    })
    
    # 盈亏信息
    total_profit_loss = Column(Float, default=0)
    total_profit_rate = Column(Float, default=0)
    day_profit_loss = Column(Float, default=0)
    day_profit_rate = Column(Float, default=0)
    
    # 其他信息
    commission = Column(Float, default=0)
    currency = Column(String(10), default="CNY")
    status = Column(String(20), default="ACTIVE")
    
    # 时间
    create_time = Column(DateTime, default=datetime.utcnow)
    update_time = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 索引
    __table_args__ = (
        Index('idx_user_account_type', 'user_id', 'account_type'),
    )
    
    # 关系
    user = relationship("User", back_populates="accounts")
    child_accounts = relationship("Account", backref="parent_account", remote_side=[id])
    simulated_stats = relationship("SimulatedAccountStats", back_populates="account", cascade="all, delete-orphan")
    simulated_executions = relationship("SimulatedExecution", back_populates="account", cascade="all, delete-orphan")


class SimulatedAccountStats(Base):
    """模拟账户统计表"""
    __tablename__ = "simulated_account_stats"
    
    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey('accounts.id'), nullable=False)
    date = Column(Date, nullable=False)
    
    # 日统计数据
    daily_pnl = Column(Float, default=0)
    daily_return = Column(Float, default=0)
    total_trades = Column(Integer, default=0)
    winning_trades = Column(Integer, default=0)
    total_commission = Column(Float, default=0)
    
    # 累计统计
    cumulative_return = Column(Float, default=0)
    max_drawdown = Column(Float, default=0)
    sharpe_ratio = Column(Float)
    win_rate = Column(Float, default=0)
    
    # 唯一约束
    __table_args__ = (
        Index('idx_account_date_unique', 'account_id', 'date', unique=True),
    )
    
    # 关系
    account = relationship("Account", back_populates="simulated_stats")


class SimulatedExecution(Base):
    """模拟成交详情表"""
    __tablename__ = "simulated_executions"
    
    id = Column(Integer, primary_key=True, index=True)
    order_id = Column(String(64), ForeignKey('orders.order_id'), nullable=False)
    account_id = Column(Integer, ForeignKey('accounts.id'), nullable=False)
    
    # 模拟成交特有信息
    market_price = Column(Float, nullable=False)
    execution_price = Column(Float, nullable=False)
    slippage = Column(Float, default=0)
    market_depth = Column(JSON)  # 成交时的市场深度快照
    execution_time = Column(DateTime, nullable=False)
    execution_delay = Column(Integer, default=0)  # 执行延迟（毫秒）
    
    # 成交算法信息
    algorithm = Column(String(20), default='MARKET')  # 'MARKET', 'LIMIT', 'ICEBERG'
    impact_cost = Column(Float, default=0)  # 市场冲击成本
    
    # 索引
    __table_args__ = (
        Index('idx_account_execution_time', 'account_id', 'execution_time'),
    )
    
    # 关系
    order = relationship("Order")
    account = relationship("Account", back_populates="simulated_executions")