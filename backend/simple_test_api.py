#!/usr/bin/env python3
"""
简单的测试API服务器
用于验证修复的功能
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

app = FastAPI(title="量化投资平台测试API", version="1.0.0")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "量化投资平台测试API运行中", "version": "1.0.0"}

@app.get("/health")
async def health():
    return {"status": "healthy", "service": "test-api"}

# 测试实时行情API
@app.get("/api/v1/market-v2/stocks")
async def get_stocks():
    return {
        "stocks": [
            {"code": "000001", "name": "平安银行", "price": 12.45, "change": 0.05},
            {"code": "000002", "name": "万科A", "price": 18.32, "change": -0.12},
            {"code": "000858", "name": "五粮液", "price": 158.66, "change": 2.45}
        ],
        "total": 3,
        "timestamp": "2025-08-01T14:30:00"
    }

@app.get("/api/v1/market-v2/quotes/{symbol}")
async def get_quote(symbol: str):
    return {
        "symbol": symbol,
        "price": 12.45,
        "change": 0.05,
        "change_percent": 0.40,
        "volume": 1234567,
        "timestamp": "2025-08-01T14:30:00"
    }

# 测试交易终端API
@app.get("/api/v1/terminal/overview")
async def get_terminal_overview():
    return {
        "account_value": 100000.00,
        "available_cash": 50000.00,
        "positions_value": 50000.00,
        "day_pnl": 1200.50,
        "total_pnl": 5600.25
    }

@app.post("/api/v1/terminal/quick-order")
async def quick_order(order_data: dict):
    return {
        "order_id": "ORD123456",
        "status": "submitted",
        "message": "订单提交成功"
    }

# 测试订单管理API
@app.get("/api/v1/orders")
async def get_orders():
    return {
        "orders": [
            {
                "id": "ORD001",
                "symbol": "000001",
                "side": "buy",
                "quantity": 100,
                "price": 12.40,
                "status": "filled",
                "timestamp": "2025-08-01T10:30:00"
            },
            {
                "id": "ORD002", 
                "symbol": "000858",
                "side": "sell",
                "quantity": 50,
                "price": 160.00,
                "status": "pending",
                "timestamp": "2025-08-01T14:15:00"
            }
        ],
        "total": 2,
        "page": 1,
        "page_size": 20
    }

@app.get("/api/v1/orders/stats")
async def get_order_stats():
    return {
        "total_orders": 156,
        "filled_orders": 142,
        "pending_orders": 8,
        "cancelled_orders": 6,
        "success_rate": 91.03,
        "total_volume": 2543680.50
    }

# 测试策略开发API
@app.post("/api/v1/strategy-dev/create")
async def create_strategy(strategy_data: dict):
    return {
        "strategy_id": "STR123456",
        "name": strategy_data.get("name", "新策略"),
        "status": "created",
        "message": "策略创建成功"
    }

@app.get("/api/v1/strategy-dev/templates")
async def get_strategy_templates():
    return {
        "templates": [
            {
                "id": "TPL001",
                "name": "移动平均策略",
                "description": "基于移动平均线的趋势跟踪策略",
                "category": "trend_following"
            },
            {
                "id": "TPL002", 
                "name": "均值回归策略",
                "description": "基于布林带的均值回归策略",
                "category": "mean_reversion"
            },
            {
                "id": "TPL003",
                "name": "网格交易策略", 
                "description": "网格式高频交易策略",
                "category": "grid_trading"
            }
        ],
        "total": 3
    }

@app.get("/api/v1/strategy-dev/marketplace")
async def get_strategy_marketplace():
    return {
        "strategies": [
            {
                "id": "MKT001",
                "name": "量化Alpha策略",
                "description": "多因子选股+Alpha策略",
                "rating": 4.5,
                "price": 299.99,
                "downloads": 1234
            },
            {
                "id": "MKT002",
                "name": "高频套利策略",
                "description": "基于统计套利的高频策略",
                "rating": 4.2,
                "price": 599.99,
                "downloads": 567
            }
        ],
        "total": 2
    }

if __name__ == "__main__":
    print("启动简单测试API服务器...")
    uvicorn.run(app, host="0.0.0.0", port=8000)