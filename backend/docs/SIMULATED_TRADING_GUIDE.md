# 模拟交易系统使用指南

## 概述

模拟交易系统是一个完整的虚拟交易环境，允许用户使用虚拟资金进行股票交易练习。系统基于真实市场数据，提供接近真实的交易体验。

## 系统架构

### 1. 数据模型设计

#### 账户模型 (Account)
- **账户类型**: 支持真实账户(REAL)和模拟账户(SIMULATED)
- **模拟账户特有字段**:
  - `initial_capital`: 初始资金（默认100万）
  - `reset_count`: 重置次数
  - `created_reason`: 创建原因（用户创建/策略测试/教学）
  - `simulated_settings`: 模拟设置（JSON）

#### 模拟执行详情 (SimulatedExecution)
- 记录每次模拟成交的详细信息
- 包含市场价格、执行价格、滑点、市场深度等
- 支持不同的执行算法和延迟模拟

#### 模拟账户统计 (SimulatedAccountStats)
- 按日统计交易业绩
- 包含盈亏、收益率、胜率、最大回撤等指标
- 支持夏普比率等高级指标计算

### 2. 核心功能模块

#### 模拟交易引擎 (SimulatedTradingEngine)
```python
# 主要功能
- create_simulated_account()  # 创建模拟账户
- submit_order()             # 提交订单
- cancel_order()             # 取消订单
- reset_account()            # 重置账户
- update_account_market_value()  # 更新市值
```

#### 执行模拟器 (ExecutionSimulator)
- 模拟真实的订单执行过程
- 支持多种滑点模式：
  - FIXED: 固定滑点
  - RANDOM: 随机滑点
  - MARKET_IMPACT: 基于市场冲击

#### 风险检查器 (RiskChecker)
- 账户状态检查
- 资金/持仓充足性检查
- 交易时间检查
- 价格合理性检查（涨跌停）
- 持仓集中度控制（单股不超过30%）

### 3. API接口设计

#### 账户管理接口 (/api/v1/account-management)
- `GET /list` - 获取账户列表
- `GET /detail/{account_id}` - 获取账户详情
- `POST /create` - 创建模拟账户
- `POST /switch` - 切换账户
- `PUT /update/{account_id}` - 更新账户信息
- `PUT /simulated/{account_id}/settings` - 更新模拟设置
- `POST /simulated/{account_id}/reset` - 重置模拟账户
- `GET /simulated/{account_id}/stats` - 获取统计数据

#### 模拟交易接口 (/api/v1/simulated-trading)
- `POST /order/submit` - 提交订单
- `POST /order/cancel` - 取消订单
- `GET /orders` - 查询订单列表
- `GET /trades` - 查询成交记录
- `GET /positions` - 查询持仓
- `GET /executions/{order_id}` - 查询成交详情
- `POST /quick-order` - 快速下单

## 使用流程

### 1. 创建模拟账户

```bash
POST /api/v1/account-management/create
{
    "account_type": "SIMULATED",
    "initial_capital": 1000000.0,
    "created_reason": "USER_CREATE",
    "settings": {
        "allow_short": false,
        "allow_margin": false,
        "slippage_mode": "RANDOM",
        "execution_delay": 300,
        "partial_fill": true,
        "market_impact_factor": 0.0001
    }
}
```

### 2. 提交交易订单

```bash
POST /api/v1/simulated-trading/order/submit
{
    "account_id": "SIM_12345678",
    "symbol": "600000",
    "direction": "BUY",
    "order_type": "LIMIT",
    "price": 10.50,
    "volume": 1000
}
```

### 3. 查询持仓

```bash
GET /api/v1/simulated-trading/positions?account_id=SIM_12345678
```

### 4. 查询账户业绩

```bash
GET /api/v1/account-management/simulated/SIM_12345678/stats?days=30
```

## 交易规则

### 基本规则
- **交易时间**: 周一至周五 9:30-11:30, 13:00-15:00
- **涨跌停限制**: ±10%
- **最小交易单位**: 100股的整数倍
- **T+1交易**: 当日买入次日才能卖出（暂未实现）

### 费用计算
- **券商佣金**: 万分之2.5，最低5元
- **印花税**: 卖出时收取千分之1
- **过户费**: 万分之0.2
- **证管费**: 万分之0.2

### 滑点模拟
系统支持三种滑点模式：
1. **固定滑点** (FIXED): 0.01%
2. **随机滑点** (RANDOM): 0-0.03%
3. **市场冲击** (MARKET_IMPACT): 基于订单量动态计算

## 高级功能

### 1. 部分成交模拟
- 限价单有30%概率部分成交
- 成交量为订单量的30%-80%
- 剩余部分继续等待成交

### 2. 执行延迟
- 默认300毫秒延迟
- 可在账户设置中调整（0-5000毫秒）

### 3. 业绩统计
- 日收益率、累计收益率
- 最大回撤
- 夏普比率
- 胜率统计

### 4. 账户重置
- 保留历史记录
- 恢复初始资金
- 清空所有持仓和未完成订单

## 测试脚本

提供了两个测试脚本：

1. **test_simulated_trading.py** - 完整功能测试
2. **test_api_simulated.py** - 简化API测试

运行测试：
```bash
cd backend
python3 test_simulated_trading.py
```

## 注意事项

1. 模拟交易不影响真实资金
2. 所有交易基于真实市场数据
3. 模拟成交会考虑市场深度和流动性
4. 建议先在模拟环境验证策略
5. 模拟账户可随时重置

## 后续优化建议

1. **T+1交易限制**: 实现当日买入次日才能卖出
2. **更真实的成交模拟**: 基于Level2行情数据
3. **期权/期货支持**: 扩展到衍生品交易
4. **策略回测集成**: 与回测系统打通
5. **多账户对比**: 支持多个模拟账户对比分析
6. **导出功能**: 支持交易记录和业绩报告导出

## 总结

模拟交易系统提供了一个安全、真实的交易练习环境。通过完整的账户管理、订单执行、风险控制和业绩统计功能，帮助用户在不承担真实风险的情况下提升交易技能。系统设计遵循了真实交易的所有规则和限制，确保用户获得最接近真实的交易体验。