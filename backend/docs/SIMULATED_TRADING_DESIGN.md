# 模拟交易账号系统设计文档

## 1. 系统概述

### 1.1 设计目标
- **真实性**: 除资金外，完全模拟真实交易环境
- **隔离性**: 模拟与真实交易数据完全隔离
- **教育性**: 帮助用户学习和验证交易策略
- **一致性**: 与真实交易使用相同的界面和逻辑

### 1.2 核心功能
- 虚拟资金账户（默认100万起始资金）
- 基于真实行情的模拟成交
- 完整的交易费用计算
- 真实的交易规则限制
- 独立的业绩统计和分析

## 2. 技术架构设计

### 2.1 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                        前端应用层                              │
│  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐      │
│  │  账户切换器   │  │  交易界面    │  │  业绩分析    │      │
│  └──────────────┘  └──────────────┘  └──────────────┘      │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                        API网关层                              │
│  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐      │
│  │ 账号管理API  │  │  交易API     │  │  行情API     │      │
│  └──────────────┘  └──────────────┘  └──────────────┘      │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                        业务逻辑层                             │
│  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐      │
│  │ 账号服务     │  │ 模拟交易引擎 │  │  风控引擎    │      │
│  └──────────────┘  └──────────────┘  └──────────────┘      │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                        数据存储层                             │
│  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐      │
│  │ 账户数据     │  │  交易数据    │  │  行情数据    │      │
│  └──────────────┘  └──────────────┘  └──────────────┘      │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 数据模型设计

#### 2.2.1 账户模型扩展
```python
class Account(Base):
    __tablename__ = "accounts"
    
    # 现有字段...
    account_type = Column(Enum('REAL', 'SIMULATED'), default='REAL')
    parent_account_id = Column(Integer, ForeignKey('accounts.id'), nullable=True)
    
    # 模拟账户特有字段
    initial_capital = Column(Float, default=1000000.0)
    reset_count = Column(Integer, default=0)
    created_reason = Column(String(50))  # 'USER_CREATE', 'STRATEGY_TEST', 'EDUCATION'
    
    # 模拟账户设置
    simulated_settings = Column(JSON, default={
        "allow_short": False,  # 是否允许做空
        "allow_margin": False,  # 是否允许融资融券
        "slippage_mode": "RANDOM",  # FIXED, RANDOM, MARKET_IMPACT
        "execution_delay": 300,  # 执行延迟（毫秒）
        "partial_fill": True,  # 是否允许部分成交
        "market_impact_factor": 0.0001  # 市场冲击因子
    })

class SimulatedAccountStats(Base):
    """模拟账户统计表"""
    __tablename__ = "simulated_account_stats"
    
    id = Column(Integer, primary_key=True)
    account_id = Column(Integer, ForeignKey('accounts.id'))
    date = Column(Date)
    
    # 日统计数据
    daily_pnl = Column(Float)
    daily_return = Column(Float)
    total_trades = Column(Integer)
    winning_trades = Column(Integer)
    total_commission = Column(Float)
    
    # 累计统计
    cumulative_return = Column(Float)
    max_drawdown = Column(Float)
    sharpe_ratio = Column(Float)
    win_rate = Column(Float)
```

#### 2.2.2 交易数据模型
```python
class SimulatedExecution(Base):
    """模拟成交详情表"""
    __tablename__ = "simulated_executions"
    
    id = Column(Integer, primary_key=True)
    order_id = Column(String, ForeignKey('orders.order_id'))
    
    # 模拟成交特有信息
    market_price = Column(Float)  # 市场价格
    execution_price = Column(Float)  # 成交价格
    slippage = Column(Float)  # 滑点
    market_depth = Column(JSON)  # 成交时的市场深度快照
    execution_time = Column(DateTime)  # 成交时间
    execution_delay = Column(Integer)  # 执行延迟（毫秒）
    
    # 成交算法信息
    algorithm = Column(String)  # 'MARKET', 'LIMIT', 'ICEBERG'
    impact_cost = Column(Float)  # 市场冲击成本
```

### 2.3 模拟交易引擎设计

#### 2.3.1 核心引擎架构
```python
class SimulatedTradingEngine:
    """模拟交易引擎"""
    
    def __init__(self):
        self.market_data_service = MarketDataService()
        self.execution_simulator = ExecutionSimulator()
        self.risk_checker = RiskChecker()
        self.commission_calculator = CommissionCalculator()
        
    async def process_order(self, order: Order, account: Account):
        """处理模拟订单"""
        # 1. 风险检查
        risk_check = await self.risk_checker.check(order, account)
        if not risk_check.passed:
            return self._reject_order(order, risk_check.reason)
            
        # 2. 获取市场数据
        market_data = await self.market_data_service.get_realtime_data(order.symbol)
        
        # 3. 模拟执行
        execution = await self.execution_simulator.simulate(order, market_data, account)
        
        # 4. 更新账户
        await self._update_account(account, execution)
        
        return execution
```

#### 2.3.2 成交模拟器
```python
class ExecutionSimulator:
    """成交模拟器"""
    
    async def simulate(self, order: Order, market_data: MarketData, account: Account):
        """模拟订单成交"""
        settings = account.simulated_settings
        
        # 1. 计算成交价格
        execution_price = self._calculate_execution_price(
            order, market_data, settings['slippage_mode']
        )
        
        # 2. 检查是否能成交
        if not self._can_execute(order, market_data, execution_price):
            return None
            
        # 3. 计算成交量（考虑部分成交）
        executed_volume = self._calculate_executed_volume(
            order, market_data, settings['partial_fill']
        )
        
        # 4. 模拟执行延迟
        await asyncio.sleep(settings['execution_delay'] / 1000)
        
        # 5. 生成成交记录
        return self._create_execution(order, execution_price, executed_volume)
        
    def _calculate_execution_price(self, order, market_data, slippage_mode):
        """计算成交价格（含滑点）"""
        base_price = market_data.current_price
        
        if slippage_mode == 'FIXED':
            slippage = 0.001  # 固定0.1%滑点
        elif slippage_mode == 'RANDOM':
            slippage = random.uniform(0, 0.002)  # 随机0-0.2%
        elif slippage_mode == 'MARKET_IMPACT':
            # 基于订单量和市场深度计算
            slippage = self._calculate_market_impact(order, market_data)
            
        if order.side == 'BUY':
            return base_price * (1 + slippage)
        else:
            return base_price * (1 - slippage)
```

### 2.4 真实性模拟要素

#### 2.4.1 交易规则引擎
```python
class TradingRuleEngine:
    """交易规则引擎"""
    
    def validate_order(self, order: Order, market_data: MarketData):
        """验证订单是否符合交易规则"""
        validations = [
            self._check_trading_time(),
            self._check_price_limit(order, market_data),
            self._check_volume_limit(order),
            self._check_suspension_status(order.symbol),
            self._check_tick_size(order),
            self._check_lot_size(order)
        ]
        
        return all(validations)
        
    def _check_price_limit(self, order, market_data):
        """检查涨跌停限制"""
        if order.order_type == 'MARKET':
            return True
            
        limit_up = market_data.pre_close * 1.10
        limit_down = market_data.pre_close * 0.90
        
        return limit_down <= order.price <= limit_up
```

#### 2.4.2 手续费计算器
```python
class CommissionCalculator:
    """手续费计算器"""
    
    def calculate(self, trade: Trade):
        """计算真实的交易费用"""
        amount = trade.price * trade.volume
        
        # 券商佣金（万分之3，最低5元）
        commission = max(amount * 0.0003, 5.0)
        
        # 印花税（卖出千分之1）
        stamp_duty = amount * 0.001 if trade.side == 'SELL' else 0
        
        # 过户费（万分之0.2）
        transfer_fee = amount * 0.00002
        
        # 证管费（万分之0.2）
        regulatory_fee = amount * 0.00002
        
        return {
            'commission': round(commission, 2),
            'stamp_duty': round(stamp_duty, 2),
            'transfer_fee': round(transfer_fee, 2),
            'regulatory_fee': round(regulatory_fee, 2),
            'total': round(commission + stamp_duty + transfer_fee + regulatory_fee, 2)
        }
```

### 2.5 API接口设计

#### 2.5.1 账号管理API
```python
@router.post("/accounts/simulated/create")
async def create_simulated_account(
    initial_capital: float = 1000000,
    reason: str = "USER_CREATE",
    settings: dict = None,
    current_user: User = Depends(get_current_active_user)
):
    """创建模拟账号"""
    # 实现逻辑...

@router.post("/accounts/switch/{account_type}")
async def switch_account(
    account_type: str,  # 'REAL' or 'SIMULATED'
    current_user: User = Depends(get_current_active_user)
):
    """切换账号类型"""
    # 实现逻辑...

@router.post("/accounts/simulated/{account_id}/reset")
async def reset_simulated_account(
    account_id: int,
    current_user: User = Depends(get_current_active_user)
):
    """重置模拟账号"""
    # 实现逻辑...
```

#### 2.5.2 模拟交易API
```python
@router.post("/simulated/orders")
async def create_simulated_order(
    order_request: OrderRequest,
    current_user: User = Depends(get_current_active_user)
):
    """创建模拟订单"""
    # 确保当前是模拟账户
    account = await get_current_account(current_user)
    if account.account_type != 'SIMULATED':
        raise HTTPException(400, "当前不是模拟账户")
        
    # 使用模拟交易引擎处理
    engine = SimulatedTradingEngine()
    result = await engine.process_order(order_request, account)
    return result
```

### 2.6 前端实现设计

#### 2.6.1 账户切换组件
```javascript
// AccountSwitcher.vue
<template>
  <div class="account-switcher">
    <el-dropdown @command="handleSwitch">
      <span class="account-info">
        <el-badge :value="accountType === 'SIMULATED' ? '模拟' : '真实'" 
                  :type="accountType === 'SIMULATED' ? 'warning' : 'success'">
          {{ currentAccount.name }}
        </el-badge>
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="REAL" :disabled="!hasRealAccount">
          <i class="el-icon-coin"></i> 真实账户
        </el-dropdown-item>
        <el-dropdown-item command="SIMULATED">
          <i class="el-icon-school"></i> 模拟账户
        </el-dropdown-item>
        <el-dropdown-item divided command="CREATE_SIMULATED">
          <i class="el-icon-plus"></i> 创建新模拟账户
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>
```

#### 2.6.2 交易界面适配
```javascript
// TradingView.vue
<template>
  <div class="trading-view">
    <!-- 模拟交易提示 -->
    <el-alert v-if="isSimulatedMode"
              title="当前为模拟交易模式"
              type="warning"
              :closable="false"
              show-icon>
      <template slot="default">
        使用虚拟资金进行交易，所有交易基于真实市场行情。
        <el-button size="mini" @click="showSimulatedGuide">了解更多</el-button>
      </template>
    </el-alert>
    
    <!-- 交易界面内容 -->
    <div class="trading-content">
      <!-- 与真实交易相同的界面 -->
    </div>
  </div>
</template>
```

### 2.7 监控和分析

#### 2.7.1 模拟交易分析
```python
class SimulatedTradingAnalyzer:
    """模拟交易分析器"""
    
    async def analyze_performance(self, account_id: int, period: str = '1M'):
        """分析模拟交易表现"""
        stats = await self._calculate_statistics(account_id, period)
        
        return {
            'overview': {
                'total_return': stats.total_return,
                'annualized_return': stats.annualized_return,
                'sharpe_ratio': stats.sharpe_ratio,
                'max_drawdown': stats.max_drawdown,
                'win_rate': stats.win_rate
            },
            'comparison': {
                'vs_market': stats.alpha,
                'vs_real_account': await self._compare_with_real(account_id)
            },
            'insights': await self._generate_insights(stats)
        }
```

## 3. 实施计划

### 第一阶段（2周）
1. 数据库模型扩展
2. 基础模拟交易引擎
3. 账户切换功能

### 第二阶段（2周）
1. 完整交易规则实现
2. 真实手续费计算
3. 市场微观结构模拟

### 第三阶段（1周）
1. 前端界面适配
2. 业绩分析功能
3. 测试和优化

### 第四阶段（1周）
1. 文档编写
2. 用户引导
3. 上线部署

## 4. 风险控制

### 4.1 数据隔离
- 使用 account_type 字段严格区分
- API层面强制检查账户类型
- 数据库层面使用视图隔离

### 4.2 用户体验
- 明确的视觉标识
- 操作确认提示
- 定期提醒当前模式

### 4.3 系统安全
- 模拟数据定期清理
- 防止数据混淆
- 审计日志记录

## 5. 总结

这个模拟交易系统设计充分考虑了真实性、教育性和用户体验，通过最小化改动现有系统来实现完整的模拟交易功能。系统将帮助用户在无风险环境下学习交易、验证策略，为真实交易做好准备。