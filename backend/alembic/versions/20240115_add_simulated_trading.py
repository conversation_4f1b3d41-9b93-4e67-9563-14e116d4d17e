"""add simulated trading support

Revision ID: add_simulated_trading
Revises: 
Create Date: 2024-01-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_simulated_trading'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # 1. 修改accounts表，添加模拟交易相关字段
    op.add_column('accounts', sa.Column('account_type', sa.Enum('REAL', 'SIMULATED', name='accounttype'), nullable=False, server_default='REAL'))
    op.add_column('accounts', sa.Column('parent_account_id', sa.Integer(), nullable=True))
    op.add_column('accounts', sa.Column('initial_capital', sa.Float(), nullable=True, server_default='1000000.0'))
    op.add_column('accounts', sa.Column('reset_count', sa.Integer(), nullable=True, server_default='0'))
    op.add_column('accounts', sa.Column('created_reason', sa.String(length=50), nullable=True))
    op.add_column('accounts', sa.Column('simulated_settings', sa.JSON(), nullable=True))
    
    # 添加外键约束
    op.create_foreign_key('fk_accounts_parent', 'accounts', 'accounts', ['parent_account_id'], ['id'])
    
    # 添加索引
    op.create_index('idx_user_account_type', 'accounts', ['user_id', 'account_type'])
    
    # 移除user_id的唯一约束（允许用户有多个账户）
    op.drop_constraint('accounts_user_id_key', 'accounts', type_='unique')
    
    # 2. 创建模拟账户统计表
    op.create_table('simulated_account_stats',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('account_id', sa.Integer(), nullable=False),
        sa.Column('date', sa.Date(), nullable=False),
        sa.Column('daily_pnl', sa.Float(), nullable=True, server_default='0'),
        sa.Column('daily_return', sa.Float(), nullable=True, server_default='0'),
        sa.Column('total_trades', sa.Integer(), nullable=True, server_default='0'),
        sa.Column('winning_trades', sa.Integer(), nullable=True, server_default='0'),
        sa.Column('total_commission', sa.Float(), nullable=True, server_default='0'),
        sa.Column('cumulative_return', sa.Float(), nullable=True, server_default='0'),
        sa.Column('max_drawdown', sa.Float(), nullable=True, server_default='0'),
        sa.Column('sharpe_ratio', sa.Float(), nullable=True),
        sa.Column('win_rate', sa.Float(), nullable=True, server_default='0'),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['account_id'], ['accounts.id'], ),
    )
    op.create_index('idx_account_date_unique', 'simulated_account_stats', ['account_id', 'date'], unique=True)
    
    # 3. 创建模拟成交详情表
    op.create_table('simulated_executions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('order_id', sa.String(length=64), nullable=False),
        sa.Column('account_id', sa.Integer(), nullable=False),
        sa.Column('market_price', sa.Float(), nullable=False),
        sa.Column('execution_price', sa.Float(), nullable=False),
        sa.Column('slippage', sa.Float(), nullable=True, server_default='0'),
        sa.Column('market_depth', sa.JSON(), nullable=True),
        sa.Column('execution_time', sa.DateTime(), nullable=False),
        sa.Column('execution_delay', sa.Integer(), nullable=True, server_default='0'),
        sa.Column('algorithm', sa.String(length=20), nullable=True, server_default='MARKET'),
        sa.Column('impact_cost', sa.Float(), nullable=True, server_default='0'),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['order_id'], ['orders.order_id'], ),
        sa.ForeignKeyConstraint(['account_id'], ['accounts.id'], ),
    )
    op.create_index('idx_account_execution_time', 'simulated_executions', ['account_id', 'execution_time'])


def downgrade():
    # 删除创建的表
    op.drop_table('simulated_executions')
    op.drop_table('simulated_account_stats')
    
    # 恢复accounts表
    op.create_unique_constraint('accounts_user_id_key', 'accounts', ['user_id'])
    op.drop_index('idx_user_account_type', table_name='accounts')
    op.drop_constraint('fk_accounts_parent', 'accounts', type_='foreignkey')
    
    op.drop_column('accounts', 'simulated_settings')
    op.drop_column('accounts', 'created_reason')
    op.drop_column('accounts', 'reset_count')
    op.drop_column('accounts', 'initial_capital')
    op.drop_column('accounts', 'parent_account_id')
    op.drop_column('accounts', 'account_type')
    
    # 删除枚举类型
    op.execute('DROP TYPE IF EXISTS accounttype')