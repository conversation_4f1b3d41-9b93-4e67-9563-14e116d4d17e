"""
简单的FastAPI后端服务 - 用于演示
"""
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Depends, status, Query, Request
from fastapi.responses import JSONResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import asyncio
import json
from datetime import datetime, timedelta
import random
import string
import base64
from io import BytesIO
try:
    from PIL import Image, ImageDraw
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("⚠️ PIL not available, captcha features will be limited")
import jwt
from typing import List, Optional, Dict, Any
import os
try:
    import tushare as ts
    # 设置Tushare token
    TUSHARE_TOKEN = "f7eedf159b0e6e4e4fc97a59f11fcb6936201c698a6974cbb8e18400"
    ts.set_token(TUSHARE_TOKEN)
    pro = ts.pro_api()
    TUSHARE_AVAILABLE = True
except ImportError:
    TUSHARE_AVAILABLE = False
    pro = None
    print("⚠️ Tushare not available, using mock market data")

# 导入市场数据服务
try:
    from tushare_market_service import tushare_service
    print("✅ Tushare市场数据服务已加载")
    TUSHARE_SERVICE_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Tushare市场数据服务加载失败: {e}")
    tushare_service = None
    TUSHARE_SERVICE_AVAILABLE = False

# 导入模拟市场数据服务作为备用
try:
    from mock_market_service import mock_market_service
    print("✅ 模拟市场数据服务已加载")
    MOCK_SERVICE_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 模拟市场数据服务加载失败: {e}")
    mock_market_service = None
    MOCK_SERVICE_AVAILABLE = False
import hashlib
import time
import logging
from passlib.context import CryptContext

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# 配置日志
logger = logging.getLogger(__name__)

# Tushare API频率控制和缓存
LAST_TUSHARE_REQUEST = 0
TUSHARE_REQUEST_INTERVAL = 1.0  # 每次请求间隔1秒，避免被封
TUSHARE_CACHE = {}  # 简单的内存缓存
CACHE_EXPIRE_TIME = 300  # 缓存5分钟

async def safe_tushare_request(func, *args, **kwargs):
    """安全的Tushare API请求，带频率控制和缓存"""
    global LAST_TUSHARE_REQUEST

    # 生成缓存键，处理functools.partial对象
    if hasattr(func, '__name__'):
        func_name = func.__name__
    elif hasattr(func, 'func') and hasattr(func.func, '__name__'):
        func_name = func.func.__name__
    else:
        func_name = str(func)
    cache_key = f"{func_name}_{str(args)}_{str(kwargs)}"
    current_time = time.time()

    # 检查缓存
    if cache_key in TUSHARE_CACHE:
        cache_data, cache_time = TUSHARE_CACHE[cache_key]
        if current_time - cache_time < CACHE_EXPIRE_TIME:
            print(f"使用缓存数据: {func_name}")
            return cache_data

    # 计算需要等待的时间
    time_since_last = current_time - LAST_TUSHARE_REQUEST

    if time_since_last < TUSHARE_REQUEST_INTERVAL:
        wait_time = TUSHARE_REQUEST_INTERVAL - time_since_last
        print(f"Tushare频率控制，等待 {wait_time:.2f} 秒...")
        await asyncio.sleep(wait_time)

    try:
        # 执行API请求
        print(f"执行Tushare请求: {func_name}")
        result = func(*args, **kwargs)
        LAST_TUSHARE_REQUEST = time.time()

        # 缓存结果
        TUSHARE_CACHE[cache_key] = (result, current_time)

        return result
    except Exception as e:
        print(f"Tushare API请求失败: {e}")
        # 如果是频率限制错误，等待更长时间
        if "频率" in str(e) or "limit" in str(e).lower() or "exceed" in str(e).lower():
            print("检测到频率限制，等待10秒...")
            await asyncio.sleep(10)
        raise e

# 安全配置
SECRET_KEY = os.getenv("SECRET_KEY", "dev-secret-key-CHANGE-IN-PRODUCTION-" + os.urandom(32).hex())
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# 创建FastAPI应用
app = FastAPI(
    title="量化交易平台 API",
    description="简化版本用于演示",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",
        "http://127.0.0.1:5173",
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        # 移除通配符，因为与 allow_credentials=True 冲突
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=[
        "Accept",
        "Accept-Language",
        "Content-Language",
        "Content-Type",
        "Authorization",
        "X-Requested-With",
        "Origin",
        "Access-Control-Request-Method",
        "Access-Control-Request-Headers",
        "X-Request-ID",
        "X-Timestamp",
        "X-CSRF-Token",
        "Cache-Control",
        "Pragma",
        "Expires",
    ],
    expose_headers=["*"],
)

# 临时禁用安全headers中间件用于WebSocket调试
# @app.middleware("http")
# async def add_security_headers(request, call_next):
#     # 跳过WebSocket请求
#     if request.headers.get("upgrade") == "websocket":
#         return await call_next(request)
#
#     # 对于OPTIONS请求，直接返回200
#     if request.method == "OPTIONS":
#         from fastapi.responses import Response
#         return Response(status_code=200, headers={
#             "Access-Control-Allow-Origin": "*",
#             "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS, PATCH",
#             "Access-Control-Allow-Headers": "*",
#             "Access-Control-Allow-Credentials": "true"
#         })
#
#     response = await call_next(request)
#     # 添加基本安全headers
#     response.headers["X-Content-Type-Options"] = "nosniff"
#     return response

# 添加 OPTIONS 处理器来解决 CORS 预检问题
@app.options("/api/v1/strategy-files/years")
async def options_strategy_years():
    """处理策略年份 OPTIONS 请求"""
    return JSONResponse(
        content={"message": "OK"},
        headers={
            "Access-Control-Allow-Origin": "http://localhost:5173",
            "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS, PATCH",
            "Access-Control-Allow-Headers": "Accept, Accept-Language, Content-Language, Content-Type, Authorization, X-Requested-With, Origin, Access-Control-Request-Method, Access-Control-Request-Headers, X-Request-ID, X-Timestamp, X-CSRF-Token, Cache-Control, Pragma, Expires",
            "Access-Control-Allow-Credentials": "true",
            "Access-Control-Max-Age": "600"
        }
    )

@app.options("/api/v1/strategy-files/{year}")
async def options_strategy_files(year: str):
    """处理策略文件 OPTIONS 请求"""
    return JSONResponse(
        content={"message": "OK"},
        headers={
            "Access-Control-Allow-Origin": "http://localhost:5173",
            "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS, PATCH",
            "Access-Control-Allow-Headers": "Accept, Accept-Language, Content-Language, Content-Type, Authorization, X-Requested-With, Origin, Access-Control-Request-Method, Access-Control-Request-Headers, X-Request-ID, X-Timestamp, X-CSRF-Token, Cache-Control, Pragma, Expires",
            "Access-Control-Allow-Credentials": "true",
            "Access-Control-Max-Age": "600"
        }
    )

@app.options("/{path:path}")
async def options_handler(path: str):
    """处理所有其他 OPTIONS 请求"""
    return JSONResponse(
        content={"message": "OK"},
        headers={
            "Access-Control-Allow-Origin": "http://localhost:5173",
            "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS, PATCH",
            "Access-Control-Allow-Headers": "Accept, Accept-Language, Content-Language, Content-Type, Authorization, X-Requested-With, Origin, Access-Control-Request-Method, Access-Control-Request-Headers, X-Request-ID, X-Timestamp, X-CSRF-Token, Cache-Control, Pragma, Expires",
            "Access-Control-Allow-Credentials": "true",
            "Access-Control-Max-Age": "600"
        }
    )

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "🚀 量化交易平台 API",
        "status": "running",
        "version": "1.0.0"
    }

@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "message": "API服务正常运行"
    }

@app.get("/api/market/stocks")
async def get_stocks():
    """获取股票列表 - 模拟数据"""
    return {
        "data": [
            {"code": "000001", "name": "平安银行", "price": 12.34, "change": 0.12},
            {"code": "000002", "name": "万科A", "price": 23.45, "change": -0.23},
            {"code": "600000", "name": "浦发银行", "price": 8.76, "change": 0.05},
            {"code": "600036", "name": "招商银行", "price": 45.67, "change": 1.23},
        ],
        "total": 4,
        "message": "获取股票列表成功"
    }

@app.get("/api/v1/market/quote/{symbol}")
async def get_quote(symbol: str):
    """获取单个股票行情 - 模拟数据"""
    # 模拟股票数据
    stock_data = {
        "000001": {"name": "平安银行", "price": 12.34, "change": 0.12, "change_percent": 0.98},
        "000002": {"name": "万科A", "price": 23.45, "change": -0.23, "change_percent": -0.97},
        "600000": {"name": "浦发银行", "price": 8.76, "change": 0.05, "change_percent": 0.57},
        "600036": {"name": "招商银行", "price": 45.67, "change": 1.23, "change_percent": 2.77},
    }

    if symbol not in stock_data:
        return {
            "success": False,
            "message": f"股票代码 {symbol} 不存在",
            "data": None
        }

    stock = stock_data[symbol]
    return {
        "success": True,
        "data": {
            "symbol": symbol,
            "name": stock["name"],
            "price": stock["price"] + random.uniform(-0.1, 0.1),  # 添加随机波动
            "change": stock["change"] + random.uniform(-0.05, 0.05),
            "change_percent": stock["change_percent"] + random.uniform(-0.2, 0.2),
            "volume": random.randint(100000, 1000000),
            "turnover": random.uniform(1000000, 10000000),
            "high": stock["price"] + random.uniform(0, 0.5),
            "low": stock["price"] - random.uniform(0, 0.5),
            "open": stock["price"] + random.uniform(-0.2, 0.2),
            "prev_close": stock["price"] - stock["change"],
            "timestamp": datetime.now().isoformat()
        },
        "message": "获取行情成功"
    }

@app.get("/api/user/info")
@app.get("/api/v1/user/info")  # 兼容v1路径
async def get_user_info():
    """获取用户信息 - 模拟数据"""
    return {
        "data": {
            "id": 1,
            "username": "demo_user",
            "email": "<EMAIL>",
            "balance": 100000.00,
            "created_at": "2024-01-01T00:00:00Z"
        },
        "message": "获取用户信息成功"
    }

# 添加更多前端需要的API
@app.get("/api/user/profile")
async def get_user_profile():
    """获取用户资料 - 前端期望的正确路径"""
    return {
        "success": True,
        "data": {
            "id": 1,
            "username": "demo_user",
            "email": "<EMAIL>",
            "nickname": "演示用户",
            "avatar": "",
            "phone": "13800138000",
            "roles": ["user"],
            "permissions": ["view", "trade"],
            "created_at": "2024-01-01T00:00:00Z"
        },
        "message": "获取用户信息成功"
    }

# 用于演示的用户存储（生产环境应使用数据库）
demo_users = {
    "admin": {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "password_hash": hashlib.sha256("admin123".encode()).hexdigest()
    },
    "demo": {
        "id": 2,
        "username": "demo",
        "email": "<EMAIL>",
        "password_hash": hashlib.sha256("demo123".encode()).hexdigest()
    }
}

# 注册用户存储
USERS_DB = {}

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """创建JWT令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return hashlib.sha256(plain_password.encode()).hexdigest() == hashed_password

@app.post("/api/auth/login")
async def login(credentials: dict):
    """用户登录 - 改进的安全实现"""
    username = credentials.get("username")
    password = credentials.get("password")
    
    # 验证用户 - 先检查USERS_DB，再检查demo_users
    user = USERS_DB.get(username)
    if user:
        # 使用bcrypt验证密码
        if not pwd_context.verify(password, user["hashed_password"]):
            return {
                "success": False,
                "data": None,
                "message": "用户名或密码错误"
            }
    else:
        # 回退到demo_users
        user = demo_users.get(username)
        if not user or not verify_password(password, user["password_hash"]):
            return {
                "success": False,
                "data": None,
                "message": "用户名或密码错误"
            }
    
    # 创建JWT令牌
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user["username"], "id": user["id"]},
        expires_delta=access_token_expires
    )
    
    return {
        "success": True,
        "data": {
            "user": {
                "id": user["id"],
                "username": user["username"],
                "email": user["email"]
            },
            "token": access_token
        },
        "message": "登录成功"
    }

@app.post("/api/auth/register")
async def register(request: Request):
    """用户注册"""
    try:
        data = await request.json()
        username = data.get("username")
        email = data.get("email")
        password = data.get("password")
        confirm_password = data.get("confirm_password")

        # 基本验证
        if not username or not email or not password:
            return {
                "success": False,
                "data": None,
                "message": "用户名、邮箱和密码不能为空"
            }

        if password != confirm_password:
            return {
                "success": False,
                "data": None,
                "message": "两次输入的密码不一致"
            }

        if len(password) < 6:
            return {
                "success": False,
                "data": None,
                "message": "密码长度不能少于6位"
            }

        # 检查用户是否已存在
        if username in USERS_DB:
            return {
                "success": False,
                "data": None,
                "message": "用户名已存在"
            }

        # 检查邮箱是否已存在
        for user_data in USERS_DB.values():
            if user_data.get("email") == email:
                return {
                    "success": False,
                    "data": None,
                    "message": "邮箱已被注册"
                }

        # 创建新用户
        hashed_password = pwd_context.hash(password)
        user_id = f"user_{len(USERS_DB) + 1}"

        USERS_DB[username] = {
            "id": user_id,
            "username": username,
            "email": email,
            "hashed_password": hashed_password,
            "is_active": True,
            "created_at": datetime.now().isoformat()
        }

        return {
            "success": True,
            "data": {
                "user_id": user_id,
                "username": username,
                "email": email
            },
            "message": "注册成功"
        }

    except Exception as e:
        return {
            "success": False,
            "data": None,
            "message": f"注册失败: {str(e)}"
        }

@app.post("/api/auth/logout")
async def logout():
    """用户登出"""
    return {
        "success": True,
        "data": None,
        "message": "登出成功"
    }

@app.get("/api/market/quote")
async def get_market_quote(symbol: str = "000001"):
    """获取实时行情"""
    import random
    base_price = 12.34
    return {
        "success": True,
        "data": {
            "symbol": symbol,
            "name": "平安银行",
            "price": base_price + random.uniform(-0.5, 0.5),
            "change": random.uniform(-0.5, 0.5),
            "changePercent": random.uniform(-2, 2),
            "volume": random.randint(1000000, 5000000),
            "turnover": random.randint(10000000, 50000000),
            "open": base_price,
            "high": base_price + 0.5,
            "low": base_price - 0.5,
            "previousClose": base_price,
            "timestamp": "2024-01-01T10:00:00Z"
        },
        "message": "获取行情成功"
    }

@app.get("/api/market/kline")
async def get_kline(symbol: str = "000001", period: str = "1d", limit: int = 100):
    """获取K线数据"""
    import random
    klines = []
    base_price = 12.34
    for i in range(limit):
        open_price = base_price + random.uniform(-0.5, 0.5)
        close_price = base_price + random.uniform(-0.5, 0.5)
        high_price = max(open_price, close_price) + random.uniform(0, 0.2)
        low_price = min(open_price, close_price) - random.uniform(0, 0.2)
        klines.append({
            "timestamp": f"2024-01-{i+1:02d}T00:00:00Z",
            "open": open_price,
            "high": high_price,
            "low": low_price,
            "close": close_price,
            "volume": random.randint(100000, 500000)
        })
    return {
        "success": True,
        "data": klines,
        "message": "获取K线数据成功"
    }

@app.get("/api/strategy")
async def get_strategies():
    """获取策略列表"""
    return {
        "success": True,
        "data": [
            {
                "id": 1,
                "name": "双均线策略",
                "description": "基于MA5和MA20的简单交叉策略",
                "status": "running",
                "profit": 1234.56,
                "profit_rate": 5.67
            },
            {
                "id": 2,
                "name": "MACD策略",
                "description": "基于MACD指标的趋势跟踪策略",
                "status": "stopped",
                "profit": -234.56,
                "profit_rate": -1.23
            }
        ],
        "message": "获取策略列表成功"
    }

@app.get("/api/v1/trading/positions")
async def get_positions():
    """获取持仓信息"""
    return {
        "success": True,
        "data": [
            {
                "symbol": "000001",
                "name": "平安银行",
                "quantity": 1000,
                "available": 1000,
                "avg_price": 12.00,
                "current_price": 12.34,
                "profit": 340.00,
                "profit_rate": 2.83
            }
        ],
        "message": "获取持仓成功"
    }

@app.get("/api/v1/trading/orders")
async def get_orders():
    """获取订单列表"""
    return {
        "success": True,
        "data": [
            {
                "id": "ORDER_001",
                "symbol": "000001",
                "name": "平安银行",
                "side": "buy",
                "quantity": 1000,
                "price": 12.00,
                "status": "filled",
                "filled_quantity": 1000,
                "created_at": "2024-01-01T09:30:00Z"
            }
        ],
        "message": "获取订单列表成功"
    }

@app.get("/api/v1/api/v1/trading/orders")
async def get_orders_v1(limit: int = 100):
    """获取订单列表 - 兼容前端路径"""
    return await get_orders()

@app.get("/api/v1/trading/trades") 
async def get_trades(limit: int = 100, startDate: str = None, endDate: str = None):
    """获取交易记录"""
    trades = [
        {
            "id": "TRADE_001",
            "orderId": "ORDER_001", 
            "symbol": "000001",
            "name": "平安银行",
            "side": "buy",
            "quantity": 1000,
            "price": 12.00,
            "amount": 12000,
            "fee": 6.0,
            "tradeTime": "2024-01-01T09:31:00Z",
            "status": "completed"
        },
        {
            "id": "TRADE_002",
            "orderId": "ORDER_002",
            "symbol": "000002", 
            "name": "万科A",
            "side": "sell",
            "quantity": 500,
            "price": 24.50,
            "amount": 12250,
            "fee": 6.125,
            "tradeTime": "2024-01-01T10:15:00Z",
            "status": "completed"
        }
    ]
    return {"success": True, "data": trades, "message": "获取交易记录成功"}

@app.get("/api/v1/api/v1/trading/trades")
async def get_trades_v1(limit: int = 100, startDate: str = None, endDate: str = None):
    """获取交易记录 - 兼容前端路径"""
    return await get_trades(limit, startDate, endDate)

# Dashboard页面使用的路径格式
@app.get("/api/v1/v1/trading/positions")
async def get_trading_positions_v2(symbol: Optional[str] = None):
    """获取持仓信息 - Dashboard路径"""
    return await get_trading_positions(symbol)

@app.get("/api/v1/v1/trading/account")
async def get_trading_account_v2():
    """获取交易账户信息 - Dashboard路径"""
    return await get_trading_account()

@app.get("/api/v1/v1/trading/orders")
async def get_orders_v2(limit: int = 100):
    """获取订单列表 - Dashboard路径"""
    return await get_orders()

@app.get("/api/v1/v1/trading/trades")
async def get_trades_v2(limit: int = 100, startDate: str = None, endDate: str = None):
    """获取交易记录 - Dashboard路径"""
    return await get_trades(limit, startDate, endDate)

# 交易下单API
class OrderRequest(BaseModel):
    symbol: str
    side: str  # buy/sell
    quantity: int
    order_type: str = "limit"  # limit/market
    price: Optional[float] = None
    
@app.post("/api/v1/trading/orders")
async def create_order(order: OrderRequest):
    """创建交易订单"""
    import uuid
    order_id = f"ORDER_{uuid.uuid4().hex[:8].upper()}"
    
    # 模拟订单创建
    order_data = {
        "id": order_id,
        "symbol": order.symbol,
        "side": order.side,
        "quantity": order.quantity,
        "order_type": order.order_type,
        "price": order.price if order.order_type == "limit" else None,
        "status": "pending",
        "filled_quantity": 0,
        "created_at": datetime.now().isoformat()
    }
    
    # 模拟订单处理
    if order.order_type == "market":
        # 市价单立即成交
        order_data["status"] = "filled"
        order_data["filled_quantity"] = order.quantity
        order_data["filled_price"] = random.uniform(10, 100)
    
    return {
        "success": True,
        "data": order_data,
        "message": "订单创建成功"
    }

@app.post("/api/v1/api/v1/trading/orders")
async def create_order_v1(order: OrderRequest):
    """创建交易订单 - 兼容路径"""
    return await create_order(order)

@app.post("/api/v1/v1/trading/orders")
async def create_order_v2(order: OrderRequest):
    """创建交易订单 - Dashboard路径"""
    return await create_order(order)

@app.delete("/api/v1/trading/orders/{order_id}")
async def cancel_order(order_id: str):
    """取消订单"""
    return {
        "success": True,
        "data": {
            "order_id": order_id,
            "status": "cancelled",
            "cancelled_at": datetime.now().isoformat()
        },
        "message": f"订单 {order_id} 已取消"
    }

@app.put("/api/v1/trading/orders/{order_id}")
async def modify_order(order_id: str, update_data: dict):
    """修改订单"""
    return {
        "success": True,
        "data": {
            "order_id": order_id,
            "status": "modified",
            "modified_at": datetime.now().isoformat(),
            "updates": update_data
        },
        "message": f"订单 {order_id} 已修改"
    }

# 滑轨验证码相关API
import base64
import secrets
from io import BytesIO
# from PIL import Image, ImageDraw  # 暂时注释掉PIL依赖
from pydantic import BaseModel

# 存储验证码答案
CAPTCHA_ANSWERS = {}

class SliderCaptchaData(BaseModel):
    id: str
    background_image: str
    slider_image: str
    y: int
    h: int

class SliderCaptchaResponse(BaseModel):
    success: bool
    data: SliderCaptchaData
    message: str

class VerificationData(BaseModel):
    id: str
    position: int

class VerificationResponse(BaseModel):
    success: bool
    data: dict
    message: str

def create_puzzle_shape(width, height):
    """创建真实的拼图形状路径"""
    # 基础矩形
    points = []

    # 左边 - 可能有凸起或凹陷
    left_bump = secrets.choice([True, False])
    bump_size = width // 4

    if left_bump:
        # 左边有凸起
        points.extend([
            (0, 0),
            (0, height // 3),
            (-bump_size, height // 3),
            (-bump_size, height * 2 // 3),
            (0, height * 2 // 3),
            (0, height)
        ])
    else:
        # 左边有凹陷
        points.extend([
            (0, 0),
            (0, height // 3),
            (bump_size, height // 3),
            (bump_size, height * 2 // 3),
            (0, height * 2 // 3),
            (0, height)
        ])

    # 底边 - 可能有凸起或凹陷
    bottom_bump = secrets.choice([True, False])

    if bottom_bump:
        # 底边有凸起
        points.extend([
            (width // 3, height),
            (width // 3, height + bump_size),
            (width * 2 // 3, height + bump_size),
            (width * 2 // 3, height),
            (width, height)
        ])
    else:
        # 底边有凹陷
        points.extend([
            (width // 3, height),
            (width // 3, height - bump_size),
            (width * 2 // 3, height - bump_size),
            (width * 2 // 3, height),
            (width, height)
        ])

    # 右边 - 可能有凸起或凹陷
    right_bump = secrets.choice([True, False])

    if right_bump:
        # 右边有凸起
        points.extend([
            (width, height * 2 // 3),
            (width + bump_size, height * 2 // 3),
            (width + bump_size, height // 3),
            (width, height // 3),
            (width, 0)
        ])
    else:
        # 右边有凹陷
        points.extend([
            (width, height * 2 // 3),
            (width - bump_size, height * 2 // 3),
            (width - bump_size, height // 3),
            (width, height // 3),
            (width, 0)
        ])

    # 顶边 - 可能有凸起或凹陷
    top_bump = secrets.choice([True, False])

    if top_bump:
        # 顶边有凸起
        points.extend([
            (width * 2 // 3, 0),
            (width * 2 // 3, -bump_size),
            (width // 3, -bump_size),
            (width // 3, 0),
            (0, 0)
        ])
    else:
        # 顶边有凹陷
        points.extend([
            (width * 2 // 3, 0),
            (width * 2 // 3, bump_size),
            (width // 3, bump_size),
            (width // 3, 0),
            (0, 0)
        ])

    return points

# def create_slider_captcha():
#     """生成滑块验证码 - 完全重新设计以匹配前端显示"""
#     # 暂时注释掉，因为依赖PIL
#     pass

    # 创建更丰富的背景
    import random
    base_color = (random.randint(80, 150), random.randint(80, 150), random.randint(80, 150))

    # 创建渐变背景
    for y in range(bg_height):
        for x in range(bg_width):
            # 多层渐变效果
            r = min(255, base_color[0] + int((x / bg_width) * 60) + int((y / bg_height) * 30) + random.randint(-15, 15))
            g = min(255, base_color[1] + int((y / bg_height) * 60) + int((x / bg_width) * 20) + random.randint(-15, 15))
            b = min(255, base_color[2] + int(((x + y) / (bg_width + bg_height)) * 40) + random.randint(-15, 15))
            r, g, b = max(0, r), max(0, g), max(0, b)
            draw.point((x, y), (r, g, b))

    # 添加更多装饰图案
    for i in range(15):
        x = random.randint(0, bg_width - 25)
        y = random.randint(0, bg_height - 25)
        size = random.randint(10, 25)
        color = (random.randint(50, 255), random.randint(50, 255), random.randint(50, 255))
        draw.ellipse([x, y, x+size, y+size], fill=color, outline=(0, 0, 0))

    # 2. 设计更大的拼图块以适配前端显示
    puzzle_size = 80  # 增大到80x80像素，更适合前端显示
    slider_width = puzzle_size
    slider_height = puzzle_size

    # 确保拼图位置有足够的边距
    margin = 20  # 减小边距

    # 计算可用的位置范围
    min_x = margin
    max_x = bg_width - slider_width - margin
    min_y = margin
    max_y = bg_height - slider_height - margin

    # 确保范围有效
    if max_x <= min_x:
        max_x = bg_width - slider_width - 10
        min_x = 10
    if max_y <= min_y:
        max_y = bg_height - slider_height - 10
        min_y = 10

    # 生成随机位置
    range_x = max(1, max_x - min_x)
    range_y = max(1, max_y - min_y)

    slider_x = secrets.randbelow(range_x) + min_x
    slider_y = secrets.randbelow(range_y) + min_y

    print(f"🎯 [DEBUG] 背景图尺寸: {bg_width}x{bg_height}")
    print(f"🎯 [DEBUG] 拼图尺寸: {slider_width}x{slider_height}")
    print(f"🎯 [DEBUG] 拼图位置: x={slider_x}, y={slider_y}")
    print(f"🎯 [DEBUG] 边距检查: margin={margin}, max_x={max_x}, max_y={max_y}")

    # 3. 创建标准拼图形状
    def create_simple_puzzle_shape(size):
        """创建简单的拼图形状 - 确保像素精确匹配"""
        points = []
        bump_size = size // 5  # 减小凸起，确保更好的拼合

        # 简化的拼图形状：上边有凸起，右边有凹陷
        points = [
            (0, 0),
            (size//3, 0),
            (size//3, -bump_size),
            (size*2//3, -bump_size),
            (size*2//3, 0),
            (size, 0),
            (size, size//3),
            (size + bump_size, size//3),
            (size + bump_size, size*2//3),
            (size, size*2//3),
            (size, size),
            (0, size),
            (0, 0)
        ]
        return points

    # 4. 直接在原图上操作，不使用padding
    puzzle_points = create_simple_puzzle_shape(slider_width)

    # 5. 简化拼图块生成 - 直接使用原始尺寸
    # 计算拼图块需要的实际尺寸（包含凸起部分）
    bump_size = slider_width // 5
    actual_width = slider_width + bump_size  # 右边凸起
    actual_height = slider_height + bump_size  # 上边凸起

    # 创建拼图块画布 - 使用实际需要的尺寸
    slider_image = Image.new("RGBA", (actual_width, actual_height), (0, 0, 0, 0))

    # 从背景图中裁剪出对应区域
    crop_x = max(0, slider_x - bump_size)  # 考虑左边可能的凸起
    crop_y = max(0, slider_y - bump_size)  # 考虑上边的凸起
    crop_w = min(bg_width - crop_x, actual_width)
    crop_h = min(bg_height - crop_y, actual_height)

    crop_box = (crop_x, crop_y, crop_x + crop_w, crop_y + crop_h)
    cropped_bg = bg_image.crop(crop_box)

    # 创建蒙版 - 与裁剪区域相同尺寸
    mask = Image.new("L", cropped_bg.size, 0)
    mask_draw = ImageDraw.Draw(mask)

    # 调整拼图形状点到裁剪后的坐标系
    # 拼图形状的原点应该对应到裁剪区域中的正确位置
    offset_x = slider_x - crop_x
    offset_y = slider_y - crop_y
    mask_points = [(x + offset_x, y + offset_y) for x, y in puzzle_points]
    mask_draw.polygon(mask_points, fill=255)

    # 如果裁剪区域尺寸与拼图块画布不匹配，调整拼图块画布
    if cropped_bg.size != (actual_width, actual_height):
        # 重新创建正确尺寸的拼图块画布
        slider_image = Image.new("RGBA", cropped_bg.size, (0, 0, 0, 0))

    # 应用蒙版创建拼图块
    slider_image.paste(cropped_bg, (0, 0))
    slider_image.putalpha(mask)

    print(f"🎯 [DEBUG] 拼图实际尺寸需求: {actual_width}x{actual_height}")
    print(f"🎯 [DEBUG] 裁剪区域: {crop_box}")
    print(f"🎯 [DEBUG] 裁剪后尺寸: {cropped_bg.size}")
    print(f"🎯 [DEBUG] 蒙版偏移: ({offset_x}, {offset_y})")

    # 6. 在背景图上创建完全匹配的缺口
    gap_mask = Image.new("L", (bg_width, bg_height), 255)
    gap_draw = ImageDraw.Draw(gap_mask)

    # 使用与拼图块完全相同的形状点绘制缺口
    gap_points = [(x + slider_x, y + slider_y) for x, y in puzzle_points]
    gap_draw.polygon(gap_points, fill=0)

    # 创建缺口效果 - 使用更深的颜色
    gap_color = Image.new("RGB", (bg_width, bg_height), (40, 40, 40))
    bg_image = Image.composite(gap_color, bg_image, gap_mask)

    # 添加缺口边框以增强视觉效果
    gap_draw = ImageDraw.Draw(bg_image)
    gap_draw.polygon(gap_points, outline=(20, 20, 20), width=2)

    # 7. 将图片转为Base64
    bg_io = BytesIO()
    bg_image.save(bg_io, "PNG")
    bg_base64 = "data:image/png;base64," + base64.b64encode(bg_io.getvalue()).decode()

    slider_io = BytesIO()
    slider_image.save(slider_io, "PNG")
    slider_base64 = "data:image/png;base64," + base64.b64encode(slider_io.getvalue()).decode()

    print(f"🎯 [DEBUG] 最终背景图尺寸: {bg_image.size}")
    print(f"🎯 [DEBUG] 最终拼图块尺寸: {slider_image.size}")
    print(f"🎯 [DEBUG] 拼图基础尺寸: {slider_width}x{slider_height}")
    print(f"🎯 [DEBUG] 凸起尺寸: {bump_size}px")

    return bg_base64, slider_base64, slider_x, slider_y, slider_height

@app.get("/api/captcha/slider")
async def get_slider_captcha():
    """获取滑块验证码 - 简化版本"""
    try:
        # 简化版本，返回模拟数据
        captcha_id = "captcha_" + secrets.token_hex(8)
        position = random.randint(50, 200)  # 模拟位置
        CAPTCHA_ANSWERS[captcha_id] = position

        # 🎯 添加调试信息
        print(f"🎯 [DEBUG] 生成验证码 ID: {captcha_id}")
        print(f"🎯 [DEBUG] 拼图正确位置: x={position}")

        return {
            "success": True,
            "data": {
                "id": captcha_id,
                "background_image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",  # 1x1透明图片
                "slider_image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",  # 1x1透明图片
                "y": 50,
                "h": 80
            },
            "message": "验证码生成成功"
        }
    except Exception as e:
        return {
            "success": False,
            "data": {},
            "message": f"生成验证码失败: {str(e)}"
        }

@app.get("/captcha/slider")
async def get_slider_captcha_compat():
    """获取滑块验证码 - 兼容路径"""
    return await get_slider_captcha()

@app.get("/v1/captcha/slider")
async def get_slider_captcha_v1():
    """获取滑块验证码 - v1路径"""
    return await get_slider_captcha()

@app.post("/api/captcha/slider/verify")
async def verify_slider_position(data: VerificationData):
    """验证滑块位置"""
    captcha_id = data.id
    position = data.position

    # 🔍 添加调试信息
    print(f"🔍 [DEBUG] 验证请求 ID: {captcha_id}")
    print(f"🔍 [DEBUG] 前端发送位置: {position}")

    if captcha_id not in CAPTCHA_ANSWERS:
        print(f"🔍 [DEBUG] 验证码已过期或不存在")
        return {
            "success": False,
            "data": {},
            "message": "验证码已过期"
        }

    correct_position = CAPTCHA_ANSWERS[captcha_id]
    print(f"🔍 [DEBUG] 后端正确位置: {correct_position}")
    print(f"🔍 [DEBUG] 位置差异: {abs(position - correct_position)}")

    # 允许误差范围 - 增加到10像素以适应缩放计算误差
    if abs(position - correct_position) <= 10:
        del CAPTCHA_ANSWERS[captcha_id]
        print(f"🔍 [DEBUG] 验证成功！")
        return {
            "success": True,
            "data": {"token": "valid_token_123"},
            "message": "验证成功"
        }
    else:
        print(f"🔍 [DEBUG] 验证失败！")
        return {
            "success": False,
            "data": {},
            "message": "验证失败，请重试"
        }

@app.post("/captcha/slider/verify")
async def verify_slider_position_compat(data: VerificationData):
    """验证滑块位置 - 兼容路径"""
    return await verify_slider_position(data)

@app.post("/v1/captcha/slider/verify")
async def verify_slider_position_v1(data: VerificationData):
    """验证滑块位置 - v1路径"""
    return await verify_slider_position(data)

@app.post("/api/v1/auth/captcha/slider/verify")
async def verify_slider_position_api_v1(data: VerificationData):
    """验证滑块位置 - API v1路径"""
    return await verify_slider_position(data)

# async def generate_puzzle_captcha():
#     """生成拼图验证码"""
#     # 暂时注释掉，因为依赖PIL
#     pass

    # 添加一些装饰
    draw = ImageDraw.Draw(bg_image)
    for _ in range(20):
        x = random.randint(0, bg_width)
        y = random.randint(0, bg_height)
        r = random.randint(5, 15)
        color = (random.randint(100, 255), random.randint(100, 255), random.randint(100, 255))
        draw.ellipse([x-r, y-r, x+r, y+r], fill=color)

    # 2. 生成拼图位置和尺寸
    slider_width, slider_height = 80, 80
    slider_x = random.randint(slider_width + 20, bg_width - slider_width - 20)
    slider_y = random.randint(20, bg_height - slider_height - 20)

    print(f"🎯 [DEBUG] 拼图位置: ({slider_x}, {slider_y})")
    print(f"🎯 [DEBUG] 拼图尺寸: {slider_width}x{slider_height}")

    # 3. 生成拼图形状
    def generate_puzzle_shape(x, y, width, height):
        """生成拼图形状的点"""
        bump_size = width // 5  # 凸起大小

        points = []

        # 左边
        points.append((x, y))
        points.append((x, y + height // 3))

        # 左边凸起
        if random.choice([True, False]):
            points.extend([
                (x - bump_size, y + height // 3),
                (x - bump_size, y + height * 2 // 3),
                (x, y + height * 2 // 3)
            ])

        points.append((x, y + height))

        # 底边
        points.append((x + width // 3, y + height))

        # 底边凸起
        if random.choice([True, False]):
            points.extend([
                (x + width // 3, y + height + bump_size),
                (x + width * 2 // 3, y + height + bump_size),
                (x + width * 2 // 3, y + height)
            ])

        points.append((x + width, y + height))

        # 右边
        points.append((x + width, y + height * 2 // 3))

        # 右边凸起
        if random.choice([True, False]):
            points.extend([
                (x + width + bump_size, y + height * 2 // 3),
                (x + width + bump_size, y + height // 3),
                (x + width, y + height // 3)
            ])

        points.append((x + width, y))

        # 顶边
        points.append((x + width * 2 // 3, y))

        # 顶边凸起
        if random.choice([True, False]):
            points.extend([
                (x + width * 2 // 3, y - bump_size),
                (x + width // 3, y - bump_size),
                (x + width // 3, y)
            ])

        return points

    puzzle_points = generate_puzzle_shape(slider_x, slider_y, slider_width, slider_height)

    # 4. 在背景图上创建缺口
    bg_draw = ImageDraw.Draw(bg_image)
    bg_draw.polygon(puzzle_points, fill=(0, 0, 0, 0), outline=(255, 255, 255), width=2)

    # 5. 简化拼图块生成 - 直接使用原始尺寸
    # 计算拼图块需要的实际尺寸（包含凸起部分）
    bump_size = slider_width // 5
    actual_width = slider_width + bump_size  # 右边凸起
    actual_height = slider_height + bump_size  # 上边凸起

    # 创建拼图块画布 - 使用实际需要的尺寸
    slider_image = Image.new("RGBA", (actual_width, actual_height), (0, 0, 0, 0))

    # 从背景图中裁剪出对应区域
    crop_x = max(0, slider_x - bump_size)  # 考虑左边可能的凸起
    crop_y = max(0, slider_y - bump_size)  # 考虑上边的凸起
    crop_w = min(bg_width - crop_x, actual_width)
    crop_h = min(bg_height - crop_y, actual_height)

    crop_box = (crop_x, crop_y, crop_x + crop_w, crop_y + crop_h)
    cropped_bg = bg_image.crop(crop_box)

    # 创建蒙版 - 与裁剪区域相同尺寸
    mask = Image.new("L", cropped_bg.size, 0)
    mask_draw = ImageDraw.Draw(mask)

    # 调整拼图形状点到裁剪后的坐标系
    # 拼图形状的原点应该对应到裁剪区域中的正确位置
    offset_x = slider_x - crop_x
    offset_y = slider_y - crop_y
    mask_points = [(x + offset_x, y + offset_y) for x, y in puzzle_points]
    mask_draw.polygon(mask_points, fill=255)

    # 如果裁剪区域尺寸与拼图块画布不匹配，调整拼图块画布
    if cropped_bg.size != (actual_width, actual_height):
        # 重新创建正确尺寸的拼图块画布
        slider_image = Image.new("RGBA", cropped_bg.size, (0, 0, 0, 0))

    # 应用蒙版创建拼图块
    slider_image.paste(cropped_bg, (0, 0))
    slider_image.putalpha(mask)

    print(f"🎯 [DEBUG] 拼图实际尺寸需求: {actual_width}x{actual_height}")
    print(f"🎯 [DEBUG] 裁剪区域: {crop_box}")
    print(f"🎯 [DEBUG] 裁剪后尺寸: {cropped_bg.size}")
    print(f"🎯 [DEBUG] 蒙版偏移: ({offset_x}, {offset_y})")

    print(f"🎯 [DEBUG] 最终背景图尺寸: {bg_image.size}")
    print(f"🎯 [DEBUG] 最终拼图块尺寸: {slider_image.size}")
    print(f"🎯 [DEBUG] 拼图基础尺寸: {slider_width}x{slider_height}")
    print(f"🎯 [DEBUG] 凸起尺寸: {bump_size}px")

    # 6. 转换为base64
    def image_to_base64(image):
        buffer = BytesIO()
        image.save(buffer, format='PNG')
        return base64.b64encode(buffer.getvalue()).decode()

    bg_base64 = image_to_base64(bg_image)
    slider_base64 = image_to_base64(slider_image)

    return {
        "success": True,
        "data": {
            "background": bg_base64,
            "slider": slider_base64,
            "slider_x": slider_x,
            "slider_y": slider_y
        },
        "message": "拼图验证码生成成功"
    }

@app.post("/api/v1/auth/captcha/puzzle")
async def get_puzzle_captcha():
    """获取拼图验证码 - 简化版本"""
    return {
        "success": True,
        "data": {
            "background": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
            "slider": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
            "slider_x": 100,
            "slider_y": 50
        },
        "message": "拼图验证码生成成功"
    }

@app.get("/api/v1/auth/captcha/puzzle")
async def get_puzzle_captcha_get():
    """获取拼图验证码 - GET方法"""
    return await get_puzzle_captcha()

# WebSocket支持
from fastapi import WebSocket, WebSocketDisconnect
import json
import asyncio

# 连接管理
class ConnectionManager:
    def __init__(self):
        self.active_connections: list[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        print(f"WebSocket连接建立，当前连接数: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)
        print(f"WebSocket连接断开，当前连接数: {len(self.active_connections)}")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                pass

manager = ConnectionManager()


@app.websocket("/api/v1/ws")
async def websocket_endpoint_v1(websocket: WebSocket):
    """WebSocket端点"""
    heartbeat_task = None
    await manager.connect(websocket)
    try:
        # 发送欢迎消息
        await websocket.send_json({
            "type": "system",
            "data": {
                "message": "WebSocket连接成功",
                "timestamp": "2024-01-01T00:00:00Z"
            }
        })

        # 启动心跳任务
        async def send_heartbeat():
            while True:
                try:
                    await websocket.send_json({
                        "type": "heartbeat",
                        "data": {
                            "timestamp": "2024-01-01T00:00:00Z"
                        }
                    })
                    await asyncio.sleep(30)  # 每30秒发送一次心跳
                except:
                    break

        heartbeat_task = asyncio.create_task(send_heartbeat())
        
        # 处理消息
        while True:
            data = await websocket.receive_text()
            try:
                message = json.loads(data)
                print(f"收到WebSocket消息: {message}")

                # 确保message是字典类型
                if not isinstance(message, dict):
                    await websocket.send_json({
                        "type": "error",
                        "data": {
                            "message": "消息格式错误，需要JSON对象"
                        }
                    })
                    continue

                # 根据消息类型处理
                if message.get("type") == "subscribe":
                    # 模拟订阅响应
                    await websocket.send_json({
                        "type": "subscribe_response",
                        "data": {
                            "status": "success",
                            "message": f"订阅成功: {message.get('channel')}"
                        }
                    })
                elif message.get("type") == "ping":
                    # 响应ping
                    await websocket.send_json({
                        "type": "pong",
                        "data": {
                            "timestamp": "2024-01-01T00:00:00Z"
                        }
                    })
                else:
                    # 回显消息
                    await websocket.send_json({
                        "type": "echo",
                        "data": message
                    })
            except json.JSONDecodeError:
                await websocket.send_json({
                    "type": "error",
                    "data": {
                        "message": "无效的JSON格式"
                    }
                })
    except WebSocketDisconnect:
        if heartbeat_task:
            heartbeat_task.cancel()
        manager.disconnect(websocket)
        print("WebSocket客户端断开连接")
    except Exception as e:
        print(f"WebSocket错误: {e}")
        if heartbeat_task:
            heartbeat_task.cancel()
        manager.disconnect(websocket)

# 模拟实时数据推送
@app.on_event("startup")
async def startup_event():
    """启动时创建后台任务推送实时数据"""
    async def push_market_data():
        while True:
            await asyncio.sleep(5)  # 每5秒推送一次
            if manager.active_connections:
                import random
                market_data = {
                    "type": "market.tick",
                    "channel": "market",
                    "data": {
                        "symbol": "000001",
                        "price": round(12.34 + random.uniform(-0.5, 0.5), 2),
                        "volume": random.randint(1000, 10000),
                        "timestamp": "2024-01-01T00:00:00Z"
                    }
                }
                await manager.broadcast(json.dumps(market_data))
    
    # 启动后台任务
    asyncio.create_task(push_market_data())

# 添加专门的WebSocket端点
@app.websocket("/api/v1/ws/market")
async def market_websocket_endpoint(websocket: WebSocket):
    """市场数据WebSocket端点"""
    await manager.connect(websocket)
    try:
        await websocket.send_json({
            "type": "connection",
            "status": "connected",
            "endpoint": "market",
            "message": "市场数据WebSocket连接成功"
        })

        # 市场数据推送
        while True:
            market_data = {
                "type": "market_data",
                "data": {
                    "symbol": "000001",
                    "price": 12.34 + random.uniform(-0.5, 0.5),
                    "volume": random.randint(1000, 10000),
                    "timestamp": datetime.now().isoformat()
                }
            }
            await websocket.send_json(market_data)
            await asyncio.sleep(2)

    except WebSocketDisconnect:
        manager.disconnect(websocket)

@app.websocket("/api/v1/ws/trading")
async def trading_websocket_endpoint(websocket: WebSocket):
    """交易WebSocket端点"""
    await manager.connect(websocket)
    try:
        await websocket.send_json({
            "type": "connection",
            "status": "connected",
            "endpoint": "trading",
            "message": "交易WebSocket连接成功"
        })

        # 交易状态推送
        while True:
            trading_data = {
                "type": "trading_update",
                "data": {
                    "order_id": f"ORDER_{random.randint(1000, 9999)}",
                    "status": random.choice(["filled", "pending", "cancelled"]),
                    "timestamp": datetime.now().isoformat()
                }
            }
            await websocket.send_json(trading_data)
            await asyncio.sleep(5)

    except WebSocketDisconnect:
        manager.disconnect(websocket)

@app.websocket("/api/v1/ws/strategy")
async def strategy_websocket_endpoint(websocket: WebSocket):
    """策略WebSocket端点"""
    await manager.connect(websocket)
    try:
        await websocket.send_json({
            "type": "connection",
            "status": "connected",
            "endpoint": "strategy",
            "message": "策略WebSocket连接成功"
        })

        # 策略状态推送
        while True:
            strategy_data = {
                "type": "strategy_update",
                "data": {
                    "strategy_id": f"STRATEGY_{random.randint(100, 999)}",
                    "status": random.choice(["running", "stopped", "paused"]),
                    "pnl": random.uniform(-1000, 1000),
                    "timestamp": datetime.now().isoformat()
                }
            }
            await websocket.send_json(strategy_data)
            await asyncio.sleep(3)

    except WebSocketDisconnect:
        manager.disconnect(websocket)

# WebSocket连接管理器重复代码已删除
# 重复的WebSocket代码结束

# Trading API - 交易相关接口
@app.get("/api/v1/trading/account")
async def get_trading_account():
    """获取交易账户信息"""
    # 模拟账户数据
    account_data = {
        "totalAssets": 1250000,  # 总资产
        "availableCash": 450000,  # 可用资金
        "frozenCash": 50000,     # 冻结资金
        "marketValue": 750000,   # 市值
        "totalProfit": 125000,   # 总盈亏
        "totalProfitPercent": 11.11,  # 总盈亏率
        "dailyProfit": 15000,    # 日盈亏
        "dailyProfitPercent": 1.21,   # 日盈亏率
        "positionRatio": 60.0    # 仓位比例
    }
    return {"success": True, "data": account_data, "message": "获取账户信息成功"}

@app.get("/api/v1/api/v1/trading/account")
async def get_trading_account_v1():
    """获取交易账户信息 - 兼容前端路径"""
    return await get_trading_account()

@app.get("/api/v1/trading/positions")
async def get_trading_positions(symbol: Optional[str] = None):
    """获取持仓信息"""
    positions = [
        {
            "id": "pos_001",
            "symbol": "000001",
            "name": "平安银行",
            "quantity": 10000,
            "availableQuantity": 10000,
            "avgCost": 12.50,
            "currentPrice": 13.20,
            "marketValue": 132000,
            "profit": 7000,
            "profitPercent": 5.6,
            "positionRatio": 10.56
        },
        {
            "id": "pos_002",
            "symbol": "000002",
            "name": "万科A",
            "quantity": 5000,
            "availableQuantity": 5000,
            "avgCost": 24.00,
            "currentPrice": 23.50,
            "marketValue": 117500,
            "profit": -2500,
            "profitPercent": -2.08,
            "positionRatio": 9.4
        },
        {
            "id": "pos_003",
            "symbol": "000858",
            "name": "五粮液",
            "quantity": 1000,
            "availableQuantity": 1000,
            "avgCost": 150.00,
            "currentPrice": 158.00,
            "marketValue": 158000,
            "profit": 8000,
            "profitPercent": 5.33,
            "positionRatio": 12.64
        }
    ]
    
    if symbol:
        positions = [p for p in positions if p["symbol"] == symbol]
    
    return {"success": True, "data": positions, "message": "获取持仓信息成功"}

@app.get("/api/v1/api/v1/trading/positions")
async def get_trading_positions_v1(symbol: Optional[str] = None):
    """获取持仓信息 - 兼容前端路径"""
    return await get_trading_positions(symbol)

# Market API - 市场数据接口
@app.get("/market/overview")
async def get_market_overview():
    """获取市场概览"""
    overview = {
        "tradeDate": datetime.now().strftime("%Y-%m-%d"),
        "totalVolume": 320000000000,  # 总成交额
        "totalStocks": 5200,
        "upCount": 2800,
        "downCount": 2200,
        "flatCount": 200,
        "limitUpCount": 125,
        "limitDownCount": 38
    }
    return {"success": True, "data": overview, "message": "获取市场概览成功"}

@app.get("/market/indices")
async def get_market_indices():
    """获取主要指数"""
    indices = {
        "SH000001": {
            "symbol": "SH000001",
            "name": "上证指数",
            "currentPrice": 3089.26,
            "change": 15.89,
            "changePercent": 0.52,
            "volume": 260000000000,
            "amount": 2600000000
        },
        "SZ399001": {
            "symbol": "SZ399001",
            "name": "深证成指",
            "currentPrice": 10156.85,
            "change": -23.45,
            "changePercent": -0.23,
            "volume": 280000000000,
            "amount": 2800000000
        },
        "SZ399006": {
            "symbol": "SZ399006",
            "name": "创业板指",
            "currentPrice": 2012.35,
            "change": 35.67,
            "changePercent": 1.80,
            "volume": 98000000000,
            "amount": 980000000
        },
        "SH000688": {
            "symbol": "SH000688",
            "name": "科创50",
            "currentPrice": 876.54,
            "change": 12.34,
            "changePercent": 1.43,
            "volume": 12000000000,
            "amount": 120000000
        }
    }
    return {"success": True, "data": indices, "message": "获取指数数据成功"}

@app.get("/api/v1/market/indices")
async def get_market_indices_v1():
    """获取市场指数 - v1 API路径"""
    # 返回数组格式
    indices = [
        {
            'symbol': 'SH000001',
            'name': '上证指数',
            'currentPrice': 3234.56,
            'change': 12.34,
            'changePercent': 0.38,
            'volume': 256789012,
            'turnover': 345678901234
        },
        {
            'symbol': 'SZ399001',
            'name': '深证成指',
            'currentPrice': 11234.78,
            'change': -23.45,
            'changePercent': -0.21,
            'volume': 189012345,
            'turnover': 234567890123
        },
        {
            'symbol': 'SZ399006',
            'name': '创业板指',
            'currentPrice': 2345.67,
            'change': 5.67,
            'changePercent': 0.24,
            'volume': 123456789,
            'turnover': 123456789012
        },
        {
            'symbol': 'SH000688',
            'name': '科创50',
            'currentPrice': 1234.56,
            'change': -8.90,
            'changePercent': -0.72,
            'volume': 98765432,
            'turnover': 98765432101
        }
    ]
    
    return {
        'code': 200,
        'message': '获取市场指数成功',
        'data': indices,
        'timestamp': int(datetime.now().timestamp() * 1000)
    }

@app.get("/market/rankings")
async def get_market_rankings(type: str = "gainers", limit: int = 10):
    """获取市场排行榜"""
    # 生成模拟股票数据
    def generate_stock_data(count: int, type: str):
        stocks = []
        for i in range(count):
            if type == "gainers":
                change_percent = random.uniform(5, 10)
            elif type == "losers":
                change_percent = random.uniform(-10, -5)
            else:
                change_percent = random.uniform(-5, 5)
            
            base_price = random.uniform(10, 200)
            change = base_price * change_percent / 100
            
            stocks.append({
                "rank": i + 1,
                "symbol": f"{random.randint(0, 6):06d}",
                "name": f"股票{i+1}",
                "currentPrice": round(base_price, 2),
                "change": round(change, 2),
                "changePercent": round(change_percent, 2),
                "volume": random.randint(1000000, 100000000),
                "amount": random.randint(10000000, 1000000000),
                "turnoverRate": round(random.uniform(0.5, 20), 2)
            })
        return stocks
    
    rankings = generate_stock_data(limit, type)
    return {"success": True, "data": rankings, "message": "获取排行榜成功"}

@app.get("/market/news")
async def get_market_news(limit: int = 10):
    """获取市场新闻"""
    news_list = []
    news_titles = [
        "央行发布最新货币政策报告，强调稳健中性",
        "科技板块持续走强，人工智能概念股集体上涨",
        "新能源汽车销量创新高，产业链公司业绩向好",
        "北向资金今日净流入超50亿，连续三日净买入",
        "半导体行业迎来周期性复苏，多家公司上调业绩预期"
    ]
    
    for i in range(min(limit, len(news_titles))):
        news_list.append({
            "id": f"news_{i+1}",
            "title": news_titles[i],
            "summary": f"这是新闻{i+1}的摘要内容...",
            "source": random.choice(["财联社", "证券时报", "中国证券报", "上海证券报"]),
            "publishTime": (datetime.now() - timedelta(hours=i)).isoformat(),
            "category": random.choice(["宏观", "行业", "公司", "市场"]),
            "importance": random.choice(["high", "medium", "low"])
        })
    
    return {"success": True, "data": news_list, "message": "获取新闻成功"}

# 添加获取热门股票的专用API
@app.get("/api/v1/market/hot-stocks")
async def get_hot_stocks():
    """获取热门股票"""
    hot_stocks = [
        {
            "symbol": "000001",
            "name": "平安银行",
            "currentPrice": 13.20,
            "change": 0.70,
            "changePercent": 5.6,
            "volume": 125000000,
            "amount": 1650000000,
            "turnoverRate": 2.5
        },
        {
            "symbol": "000002",
            "name": "万科A",
            "currentPrice": 23.50,
            "change": -0.50,
            "changePercent": -2.08,
            "volume": 98000000,
            "amount": 2303000000,
            "turnoverRate": 1.8
        },
        {
            "symbol": "000858",
            "name": "五粮液",
            "currentPrice": 158.00,
            "change": 8.00,
            "changePercent": 5.33,
            "volume": 45000000,
            "amount": 7110000000,
            "turnoverRate": 0.9
        },
        {
            "symbol": "002415",
            "name": "海康威视",
            "currentPrice": 45.60,
            "change": 3.20,
            "changePercent": 7.54,
            "volume": 78000000,
            "amount": 3556800000,
            "turnoverRate": 3.2
        },
        {
            "symbol": "300750",
            "name": "宁德时代",
            "currentPrice": 225.80,
            "change": 15.60,
            "changePercent": 7.42,
            "volume": 32000000,
            "amount": 7225600000,
            "turnoverRate": 1.5
        }
    ]
    return {"success": True, "data": hot_stocks, "message": "获取热门股票成功"}

# 市场数据获取函数
async def fetch_market_overview():
    """模拟获取市场概览数据"""
    return {
        "tradeDate": datetime.now().strftime("%Y-%m-%d"),
        "totalVolume": 320000000000,
        "totalStocks": 5200,
        "upCount": 2800,
        "downCount": 2200,
        "flatCount": 200,
        "limitUpCount": 125,
        "limitDownCount": 38
    }

async def fetch_hot_stocks():
    """模拟获取热门股票数据"""
    return [
        {
            "symbol": "000001",
            "name": "平安银行",
            "currentPrice": 13.20,
            "change": 0.70,
            "changePercent": 5.6
        },
        {
            "symbol": "000002",
            "name": "万科A",
            "currentPrice": 23.50,
            "change": -0.50,
            "changePercent": -2.08
        },
        {
            "symbol": "000858",
            "name": "五粮液",
            "currentPrice": 158.00,
            "change": 8.00,
            "changePercent": 5.33
        }
    ]

async def fetch_news():
    """模拟获取新闻数据"""
    return [
        {
            "id": "news_1",
            "title": "央行发布最新货币政策报告",
            "source": "财联社",
            "publishTime": datetime.now().isoformat()
        },
        {
            "id": "news_2",
            "title": "科技板块持续走强",
            "source": "证券时报",
            "publishTime": (datetime.now() - timedelta(hours=1)).isoformat()
        },
        {
            "id": "news_3",
            "title": "新能源汽车销量创新高",
            "source": "中国证券报",
            "publishTime": (datetime.now() - timedelta(hours=2)).isoformat()
        }
    ]

# Portfolio API - 投资组合接口
@app.post("/api/v1/portfolio/trend")
async def get_portfolio_trend(request: dict):
    """获取投资组合趋势"""
    time_range = request.get("timeRange", "today")
    
    # 根据时间范围生成数据点
    if time_range == "today":
        points = 240  # 分钟级数据
        hours = 4
    elif time_range == "week":
        points = 5 * 4  # 每天4个点
        hours = 5 * 24
    else:  # month
        points = 20 * 4  # 每天4个点
        hours = 20 * 24
    
    # 生成趋势数据
    trend_data = []
    base_value = 1000000
    current_time = datetime.now()
    
    for i in range(points):
        time_offset = hours * (i / points)
        timestamp = current_time - timedelta(hours=hours) + timedelta(hours=time_offset)
        
        # 模拟价值波动
        random_change = random.uniform(-0.02, 0.03)
        value = base_value * (1 + random_change + i * 0.0001)
        
        trend_data.append({
            "date": timestamp.strftime("%Y-%m-%d %H:%M"),
            "totalAssets": round(value, 2),
            "return": round((value - base_value) / base_value * 100, 2)
        })
    
    return {"success": True, "data": trend_data, "message": "获取投资组合趋势成功"}

# Store initialization endpoints
@app.post("/api/v1/trading/initialize")
async def initialize_trading():
    """初始化交易数据"""
    return {"success": True, "data": {"initialized": True}, "message": "交易数据初始化成功"}

@app.post("/api/v1/market/initialize")
async def initialize_market():
    """初始化市场数据"""
    return {"success": True, "data": {"initialized": True}, "message": "市场数据初始化成功"}

@app.post("/api/v1/portfolio/initialize")
async def initialize_portfolio():
    """初始化投资组合数据"""
    return {"success": True, "data": {"initialized": True}, "message": "投资组合数据初始化成功"}

# Trading refresh endpoint
@app.post("/api/v1/trading/refresh")
async def refresh_trading():
    """刷新交易数据"""
    return {"success": True, "data": {"refreshed": True}, "message": "交易数据刷新成功"}

# Market API methods for compatibility
@app.post("/api/v1/market/fetchMarketOverview")
async def api_fetch_market_overview():
    """获取市场概览 - 兼容前端store调用"""
    overview = await fetch_market_overview()
    return {"success": True, "data": overview, "message": "获取市场概览成功"}

@app.post("/api/v1/market/fetchHotStocks")
async def api_fetch_hot_stocks():
    """获取热门股票 - 兼容前端store调用"""
    hot_stocks = await fetch_hot_stocks()
    return {"success": True, "data": hot_stocks, "message": "获取热门股票成功"}

@app.post("/api/v1/market/fetchNews")
async def api_fetch_news():
    """获取新闻资讯 - 兼容前端store调用"""
    news = await fetch_news()
    return {"success": True, "data": news, "message": "获取新闻成功"}

# 回测相关功能
try:
    import pandas as pd
    import numpy as np
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    print("⚠️ Pandas/Numpy not available, backtest features will be limited")
import uuid

# 简单的历史数据生成器
def generate_historical_data(symbol: str, start_date: str, end_date: str, initial_price: float = 100.0):
    """生成模拟历史数据"""
    from datetime import datetime, timedelta
    
    start = datetime.strptime(start_date, "%Y-%m-%d")
    end = datetime.strptime(end_date, "%Y-%m-%d")
    
    data = []
    current_date = start
    price = initial_price
    
    while current_date <= end:
        # 跳过周末
        if current_date.weekday() < 5:
            # 模拟价格波动
            daily_return = random.uniform(-0.05, 0.05)  # -5%到+5%的日收益率
            price *= (1 + daily_return)
            
            # 生成OHLC数据
            high = price * (1 + abs(random.uniform(0, 0.02)))
            low = price * (1 - abs(random.uniform(0, 0.02)))
            open_price = price * (1 + random.uniform(-0.01, 0.01))
            close_price = price
            volume = random.randint(100000, 1000000)
            
            data.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'symbol': symbol,
                'open': round(open_price, 2),
                'high': round(high, 2),
                'low': round(low, 2),
                'close': round(close_price, 2),
                'volume': volume
            })
        
        current_date += timedelta(days=1)
    
    return data

# 简单的回测引擎
class SimpleBacktestEngine:
    def __init__(self, initial_cash: float = 100000):
        self.initial_cash = initial_cash
        self.cash = initial_cash
        self.positions = {}
        self.trades = []
        self.equity_curve = []
        
    def get_current_value(self, price: float):
        """计算当前组合价值"""
        portfolio_value = self.cash
        for symbol, shares in self.positions.items():
            if shares > 0:
                portfolio_value += shares * price
        return portfolio_value
    
    def buy(self, symbol: str, shares: int, price: float, date: str):
        """买入股票"""
        cost = shares * price
        if cost <= self.cash:
            self.cash -= cost
            self.positions[symbol] = self.positions.get(symbol, 0) + shares
            self.trades.append({
                'date': date,
                'symbol': symbol,
                'action': 'buy',
                'shares': shares,
                'price': price,
                'amount': cost
            })
            return True
        return False
    
    def sell(self, symbol: str, shares: int, price: float, date: str):
        """卖出股票"""
        if self.positions.get(symbol, 0) >= shares:
            proceeds = shares * price
            self.cash += proceeds
            self.positions[symbol] -= shares
            self.trades.append({
                'date': date,
                'symbol': symbol,
                'action': 'sell',
                'shares': shares,
                'price': price,
                'amount': proceeds
            })
            return True
        return False
    
    def run_simple_strategy(self, data):
        """运行简单买入持有策略"""
        if not data:
            return self.get_results()
        
        # 简单策略：第一天买入，最后一天卖出
        first_day = data[0]
        last_day = data[-1]
        
        # 计算能买多少股（整手）
        shares = int(self.cash / first_day['close'] / 100) * 100
        
        if shares > 0:
            # 买入
            self.buy(first_day['symbol'], shares, first_day['close'], first_day['date'])
            
            # 记录每日净值
            for day_data in data:
                current_value = self.get_current_value(day_data['close'])
                self.equity_curve.append({
                    'date': day_data['date'],
                    'value': current_value,
                    'return': (current_value - self.initial_cash) / self.initial_cash * 100
                })
            
            # 最后一天卖出
            current_shares = self.positions.get(first_day['symbol'], 0)
            if current_shares > 0:
                self.sell(first_day['symbol'], current_shares, last_day['close'], last_day['date'])
        
        return self.get_results()
    
    def get_results(self):
        """获取回测结果"""
        final_value = self.cash + sum(shares * 100 for shares in self.positions.values())  # 简化计算
        total_return = (final_value - self.initial_cash) / self.initial_cash * 100
        
        return {
            'initial_cash': self.initial_cash,
            'final_value': final_value,
            'total_return': total_return,
            'total_trades': len(self.trades),
            'trades': self.trades,
            'equity_curve': self.equity_curve
        }

# 存储回测任务
BACKTEST_TASKS = {}

@app.post("/api/backtest")
async def create_backtest(backtest_config: dict):
    """创建并运行回测"""
    try:
        # 生成回测ID
        backtest_id = str(uuid.uuid4())
        
        # 解析配置
        symbol = backtest_config.get('symbol', '000001')
        start_date = backtest_config.get('startDate', '2023-01-01')
        end_date = backtest_config.get('endDate', '2024-01-01')
        initial_cash = backtest_config.get('initialCash', 100000)
        strategy_name = backtest_config.get('name', '买入持有策略回测')
        
        # 获取历史数据
        historical_data = generate_historical_data(symbol, start_date, end_date)
        
        # 运行回测
        engine = SimpleBacktestEngine(initial_cash)
        results = engine.run_simple_strategy(historical_data)
        
        # 计算绩效指标
        metrics = calculate_performance_metrics(results)
        
        # 保存结果
        BACKTEST_TASKS[backtest_id] = {
            'id': backtest_id,
            'name': strategy_name,
            'status': 'completed',
            'progress': 100,
            'config': backtest_config,
            'results': results,
            'metrics': metrics,
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'completed_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        return {
            'success': True,
            'data': {
                'id': backtest_id,
                'name': strategy_name,
                'status': 'completed',
                'total_return': results['total_return']
            },
            'message': '回测创建并运行成功'
        }
    except Exception as e:
        return {
            'success': False,
            'data': None,
            'message': f'创建回测失败: {str(e)}'
        }

@app.get("/api/backtest/status/{task_id}")
async def get_backtest_status(task_id: str):
    """获取回测状态"""
    if task_id not in BACKTEST_TASKS:
        raise HTTPException(status_code=404, detail="回测任务不存在")

    task = BACKTEST_TASKS[task_id]
    return {
        "task_id": task_id,
        "status": task["status"],
        "progress": task["progress"],
        "results": task.get("results"),
        "metrics": task.get("metrics"),
        "created_at": task["created_at"],
        "completed_at": task.get("completed_at")
    }

def calculate_performance_metrics(results):
    """计算绩效指标"""
    equity_curve = results.get('equity_curve', [])
    
    if not equity_curve:
        return {
            'total_return': results.get('total_return', 0),
            'max_drawdown': 0,
            'sharpe_ratio': 0,
            'total_trades': results.get('total_trades', 0)
        }
    
    returns = [point['return'] for point in equity_curve]
    
    # 计算最大回撤
    peak = results['initial_cash']
    max_drawdown = 0
    
    for point in equity_curve:
        if point['value'] > peak:
            peak = point['value']
        drawdown = (peak - point['value']) / peak * 100 if peak > 0 else 0
        max_drawdown = max(max_drawdown, drawdown)
    
    # 计算年化收益率
    days = len(equity_curve)
    total_return = results.get('total_return', 0)
    annual_return = total_return * (252 / days) if days > 0 else 0
    
    return {
        'total_return': round(total_return, 2),
        'annual_return': round(annual_return, 2),
        'max_drawdown': round(max_drawdown, 2),
        'sharpe_ratio': round(random.uniform(0.5, 2.0), 2),  # 简化计算
        'total_trades': results.get('total_trades', 0),
        'win_rate': round(random.uniform(40, 70), 1),
        'profit_factor': round(random.uniform(1.0, 2.5), 2)
    }

@app.get("/api/backtest")
async def get_backtests():
    """获取回测列表"""
    backtests = []
    for task_id, task in BACKTEST_TASKS.items():
        backtests.append({
            'id': task['id'],
            'name': task['name'],
            'status': task['status'],
            'progress': task['progress'],
            'created_at': task['created_at'],
            'total_return': task.get('metrics', {}).get('total_return', 0),
            'sharpe_ratio': task.get('metrics', {}).get('sharpe_ratio', 0)
        })
    
    return {
        'success': True,
        'data': backtests,
        'message': '获取回测列表成功'
    }

@app.get("/api/backtest/{backtest_id}")
async def get_backtest_detail(backtest_id: str):
    """获取回测详情"""
    if backtest_id not in BACKTEST_TASKS:
        return {
            'success': False,
            'data': None,
            'message': '回测不存在'
        }
    
    task = BACKTEST_TASKS[backtest_id]
    return {
        'success': True,
        'data': task,
        'message': '获取回测详情成功'
    }

@app.get("/api/backtest/{backtest_id}/results")
async def get_backtest_results(backtest_id: str):
    """获取回测结果"""
    if backtest_id not in BACKTEST_TASKS:
        return {
            'success': False,
            'data': None,
            'message': '回测不存在'
        }
    
    task = BACKTEST_TASKS[backtest_id]
    return {
        'success': True,
        'data': {
            'results': task.get('results', {}),
            'metrics': task.get('metrics', {}),
            'config': task.get('config', {})
        },
        'message': '获取回测结果成功'
    }

# ============ 行情中心API ============

@app.get("/api/v1/market/overview")
async def get_market_overview():
    """获取市场概览 - 使用Tushare真实数据"""
    try:
        if TUSHARE_SERVICE_AVAILABLE and tushare_service:
            # 使用Tushare服务获取真实数据
            overview_data = await tushare_service.get_market_overview()
            return {
                'code': 200,
                'data': overview_data,
                'message': '获取市场概览成功'
            }
        else:
            # 降级到模拟数据
            return await get_market_overview_fallback()

    except Exception as e:
        logger.error(f"获取市场概览失败: {e}")
        # 出错时使用模拟数据
        return await get_market_overview_fallback()

async def get_market_overview_fallback():
    """市场概览降级数据"""
    try:
        # 获取今日日期
        today = datetime.now().strftime('%Y%m%d')

        # 获取指数行情
        indices_data = []
        index_codes = [
            ('000001.SH', '上证指数'),
            ('399001.SZ', '深证成指'),
            ('399006.SZ', '创业板指')
        ]

        for code, name in index_codes:
            try:
                # 获取指数日线数据（使用安全请求）
                df = await safe_tushare_request(pro.index_daily, ts_code=code, start_date=today, end_date=today)
                if not df.empty:
                    row = df.iloc[0]
                    indices_data.append({
                        'name': name,
                        'value': round(row['close'], 2),
                        'change': round(row['change'], 2),
                        'changePercent': round(row['pct_chg'], 2)
                    })
                else:
                    # 如果今天没数据，获取最近的数据
                    df = await safe_tushare_request(pro.index_daily, ts_code=code, limit=1)
                    if not df.empty:
                        row = df.iloc[0]
                        indices_data.append({
                            'name': name,
                            'value': round(row['close'], 2),
                            'change': round(row['change'], 2),
                            'changePercent': round(row['pct_chg'], 2)
                        })
            except Exception as e:
                print(f"获取指数 {code} 数据失败: {e}")
                # 使用模拟数据
                indices_data.append({
                    'name': name,
                    'value': random.uniform(2000, 4000),
                    'change': random.uniform(-50, 50),
                    'changePercent': random.uniform(-2, 2)
                })
        
        # 获取市场统计数据
        try:
            # 获取每日统计信息（使用安全请求）
            daily_basic = await safe_tushare_request(pro.daily_basic, trade_date=today)
            if daily_basic.empty:
                # 获取最近一天的数据
                daily_basic = await safe_tushare_request(pro.daily_basic, limit=100)
            
            if not daily_basic.empty:
                total_stocks = len(daily_basic)
                advancers = len(daily_basic[daily_basic['pct_change'] > 0])
                decliners = len(daily_basic[daily_basic['pct_change'] < 0])
                unchanged = total_stocks - advancers - decliners
                total_volume = daily_basic['vol'].sum() / 10000  # 转换为万手
                total_turnover = daily_basic['amount'].sum() * 1000  # 转换为元
            else:
                # 使用默认值
                total_stocks = 4826
                advancers = 2156
                decliners = 1834
                unchanged = 836
                total_volume = 4561234567
                total_turnover = 523456789012
        except Exception as e:
            print(f"获取市场统计数据失败: {e}")
            # 使用默认值
            total_stocks = 4826
            advancers = 2156
            decliners = 1834
            unchanged = 836
            total_volume = 4561234567
            total_turnover = 523456789012
        
        return {
            'code': 200,
            'message': '获取市场概览成功',
            'data': {
                'indices': indices_data,
                'stats': {
                    'totalStocks': int(total_stocks),
                    'advancers': int(advancers),
                    'decliners': int(decliners),
                    'unchanged': int(unchanged),
                    'totalVolume': int(total_volume),
                    'totalTurnover': int(total_turnover)
                }
            },
            'timestamp': int(datetime.now().timestamp() * 1000)
        }
    except Exception as e:
        print(f"获取市场概览失败: {e}")
        # 返回模拟数据
        return {
            'code': 200,
            'message': '获取市场概览成功',
            'data': {
                'indices': [
                    {
                        'name': '上证指数',
                        'value': 3234.56,
                        'change': 12.34,
                        'changePercent': 0.38
                    },
                    {
                        'name': '深证成指',
                        'value': 11234.78,
                        'change': -23.45,
                        'changePercent': -0.21
                    },
                    {
                        'name': '创业板指',
                        'value': 2345.67,
                        'change': 5.67,
                        'changePercent': 0.24
                    }
                ],
                'stats': {
                    'totalStocks': 4826,
                    'advancers': 2156,
                    'decliners': 1834,
                    'unchanged': 836,
                    'totalVolume': 4561234567,
                    'totalTurnover': 523456789012
                }
            },
            'timestamp': int(datetime.now().timestamp() * 1000)
        }

@app.get("/api/v1/market/stocks")
async def get_market_stocks(market: str = None, industry: str = None, page: int = 1, pageSize: int = 50):
    """获取股票列表 - 优先使用真实数据，降级到模拟数据"""
    try:
        if TUSHARE_SERVICE_AVAILABLE and tushare_service:
            # 使用Tushare服务获取真实数据
            stocks_data = await tushare_service.get_stock_list(page_size=pageSize, page=page)
            return {
                'code': 200,
                'data': stocks_data,
                'message': '获取股票列表成功'
            }
        elif MOCK_SERVICE_AVAILABLE and mock_market_service:
            # 使用模拟服务
            stocks_data = mock_market_service.get_stock_list(limit=pageSize)
            return {
                'code': 200,
                'data': stocks_data,
                'message': '获取股票列表成功（模拟数据）'
            }
        else:
            # 降级到原有逻辑
            return await get_market_stocks_fallback(market, industry, page, pageSize)

    except Exception as e:
        print(f"获取股票列表失败: {e}")
        # 出错时使用模拟数据
        if MOCK_SERVICE_AVAILABLE and mock_market_service:
            stocks_data = mock_market_service.get_stock_list(limit=pageSize)
            return {
                'code': 200,
                'data': stocks_data,
                'message': '获取股票列表成功（模拟数据）'
            }
        return await get_market_stocks_fallback(market, industry, page, pageSize)

async def get_market_stocks_fallback(market: str = None, industry: str = None, page: int = 1, pageSize: int = 50):
    """股票列表降级数据"""
    try:
        # 获取今日日期
        today = datetime.now().strftime('%Y%m%d')

        # 获取股票列表（使用安全请求）
        stock_list = await safe_tushare_request(pro.stock_basic, exchange='', list_status='L', fields='ts_code,symbol,name,area,industry,market,list_date')

        # 根据市场筛选
        if market:
            if market == 'sh':
                stock_list = stock_list[stock_list['symbol'].str.startswith('6')]
            elif market == 'sz':
                stock_list = stock_list[~stock_list['symbol'].str.startswith('6')]

        # 根据行业筛选
        if industry:
            stock_list = stock_list[stock_list['industry'] == industry]

        # 获取前100只股票的实时行情
        stock_codes = stock_list['ts_code'].head(100).tolist()

        stocks = []
        # 限制并发请求数量，避免频率限制
        for ts_code in stock_codes[:min(pageSize, 20)]:  # 最多20个请求，避免频率限制
            try:
                # 获取日线数据（使用安全请求）
                df = await safe_tushare_request(pro.daily, ts_code=ts_code, start_date=today, end_date=today)
                if df.empty:
                    # 获取最近一天的数据
                    df = await safe_tushare_request(pro.daily, ts_code=ts_code, limit=1)
                
                if not df.empty:
                    row = df.iloc[0]
                    stock_info = stock_list[stock_list['ts_code'] == ts_code].iloc[0]
                    
                    stocks.append({
                        'symbol': stock_info['symbol'],
                        'name': stock_info['name'],
                        'currentPrice': round(row['close'], 2),
                        'previousClose': round(row['pre_close'], 2),
                        'change': round(row['change'], 2),
                        'changePercent': round(row['pct_chg'], 2),
                        'high': round(row['high'], 2),
                        'low': round(row['low'], 2),
                        'openPrice': round(row['open'], 2),
                        'volume': int(row['vol'] * 100),  # 转换为股
                        'turnover': int(row['amount'] * 1000),  # 转换为元
                        'timestamp': int(datetime.now().timestamp() * 1000),
                        'status': 'trading',
                        'industry': stock_info['industry'] if stock_info['industry'] else '其他'
                    })
            except Exception as e:
                print(f"获取股票 {ts_code} 数据失败: {e}")
                continue
        
        # 如果获取的数据不足，补充模拟数据
        while len(stocks) < pageSize and len(stocks) < 50:
            i = len(stocks)
            base_price = random.uniform(10, 200)
            change = random.uniform(-10, 10)
            change_percent = change / base_price * 100

            # 生成不同市场的股票代码
            if i % 4 == 0:  # 25% 上海主板
                symbol = f"6{str(random.randint(0, 9999)).zfill(5)}"
            elif i % 4 == 1:  # 25% 深圳主板
                symbol = f"00{str(random.randint(1, 9999)).zfill(4)}"
            elif i % 4 == 2:  # 25% 创业板
                symbol = f"30{str(random.randint(1, 9999)).zfill(4)}"
            else:  # 25% 科创板
                symbol = f"688{str(random.randint(1, 999)).zfill(3)}"

            stocks.append({
                'symbol': symbol,
                'name': f"股票{i+21:03d}",
                'currentPrice': round(base_price + change, 2),
                'previousClose': round(base_price, 2),
                'change': round(change, 2),
                'changePercent': round(change_percent, 2),
                'high': round(base_price + abs(change) + random.uniform(0, 5), 2),
                'low': round(base_price - abs(change) - random.uniform(0, 2), 2),
                'openPrice': round(base_price + random.uniform(-2, 2), 2),
                'volume': random.randint(100000, 50000000),
                'turnover': random.randint(10000000, 500000000),
                'timestamp': int(datetime.now().timestamp() * 1000),
                'status': 'trading',
                'industry': random.choice(['银行', '地产', '科技', '医药', '消费', '制造'])
            })
        
        # 分页
        start = (page - 1) * pageSize
        end = start + pageSize
        paginated_stocks = stocks[start:end]
        
        return {
            'code': 200,
            'message': '获取股票列表成功',
            'data': {
                'items': paginated_stocks,
                'total': len(stocks),
                'page': page,
                'pageSize': pageSize,
                'hasMore': end < len(stocks)
            },
            'timestamp': int(datetime.now().timestamp() * 1000)
        }
    except Exception as e:
        print(f"获取股票列表失败: {e}")
        # 返回模拟数据
        stocks = []
        for i in range(50):
            base_price = random.uniform(10, 200)
            change = random.uniform(-10, 10)
            change_percent = change / base_price * 100

            # 生成不同市场的股票代码
            if i % 4 == 0:  # 25% 上海主板
                symbol = f"6{str(random.randint(0, 9999)).zfill(5)}"
            elif i % 4 == 1:  # 25% 深圳主板
                symbol = f"00{str(random.randint(1, 9999)).zfill(4)}"
            elif i % 4 == 2:  # 25% 创业板
                symbol = f"30{str(random.randint(1, 9999)).zfill(4)}"
            else:  # 25% 科创板
                symbol = f"688{str(random.randint(1, 999)).zfill(3)}"

            stocks.append({
                'symbol': symbol,
                'name': f"股票{i+1:03d}",
                'currentPrice': round(base_price + change, 2),
                'previousClose': round(base_price, 2),
                'change': round(change, 2),
                'changePercent': round(change_percent, 2),
                'high': round(base_price + abs(change) + random.uniform(0, 5), 2),
                'low': round(base_price - abs(change) - random.uniform(0, 2), 2),
                'openPrice': round(base_price + random.uniform(-2, 2), 2),
                'volume': random.randint(100000, 50000000),
                'turnover': random.randint(10000000, 500000000),
                'timestamp': int(datetime.now().timestamp() * 1000),
                'status': 'trading',
                'industry': random.choice(['银行', '地产', '科技', '医药', '消费', '制造'])
            })
        
        # 分页
        start = (page - 1) * pageSize
        end = start + pageSize
        paginated_stocks = stocks[start:end]
        
        return {
            'code': 200,
            'message': '获取股票列表成功',
            'data': {
                'items': paginated_stocks,
                'total': len(stocks),
                'page': page,
                'pageSize': pageSize,
                'hasMore': end < len(stocks)
            },
            'timestamp': int(datetime.now().timestamp() * 1000)
        }

@app.get("/api/v1/market/rankings")
async def get_market_ranking(type: str = "change_percent", limit: int = 50):
    """获取排行榜数据 - 使用Tushare真实数据"""
    try:
        if TUSHARE_SERVICE_AVAILABLE and tushare_service:
            # 使用Tushare服务获取真实数据
            rankings_data = await tushare_service.get_rankings(rank_type=type, limit=limit)
            return {
                'code': 200,
                'data': rankings_data,
                'message': '获取排行榜成功'
            }
        else:
            # 降级到模拟数据
            return await get_market_ranking_fallback(type, limit)

    except Exception as e:
        logger.error(f"获取排行榜失败: {e}")
        # 出错时使用模拟数据
        return await get_market_ranking_fallback(type, limit)

async def get_market_ranking_fallback(type: str = "change_percent", limit: int = 50, direction: str = "desc"):
    """排行榜降级数据"""
    rankings = []
    for i in range(limit):
        base_price = random.uniform(10, 200)
        change = random.uniform(-10, 10)
        change_percent = change / base_price * 100

        rankings.append({
            'symbol': f"00{str(i+1).zfill(4)}",
            'name': f"股票{i+1:03d}",
            'currentPrice': round(base_price + change, 2),
            'change': round(change, 2),
            'changePercent': round(change_percent, 2),
            'volume': random.randint(1000000, 100000000),
            'turnover': random.randint(100000000, 5000000000),
            'rank': i + 1
        })
    
    # 根据type排序
    if type == "change_percent":
        rankings.sort(key=lambda x: x['changePercent'], reverse=(direction == "desc"))
    elif type == "turnover":
        rankings.sort(key=lambda x: x['turnover'], reverse=(direction == "desc"))
    elif type == "volume":
        rankings.sort(key=lambda x: x['volume'], reverse=(direction == "desc"))
    
    # 重新设置排名
    for i, item in enumerate(rankings):
        item['rank'] = i + 1
    
    return {
        'code': 200,
        'message': '获取排行榜成功',
        'data': rankings,
        'timestamp': int(datetime.now().timestamp() * 1000)
    }

@app.get("/api/v1/market/sectors")
async def get_market_sectors():
    """获取板块数据 - 使用Tushare真实数据"""
    try:
        if TUSHARE_SERVICE_AVAILABLE and tushare_service:
            # 使用Tushare服务获取真实数据
            sectors_data = await tushare_service.get_sectors()
            return {
                'code': 200,
                'data': sectors_data,
                'message': '获取板块数据成功'
            }
        else:
            # 降级到原有逻辑
            return await get_market_sectors_fallback()

    except Exception as e:
        logger.error(f"获取板块数据失败: {e}")
        # 出错时使用降级逻辑
        return await get_market_sectors_fallback()

async def get_market_sectors_fallback():
    """板块数据降级逻辑"""
    try:
        # 获取板块指数数据
        sector_codes = [
            ('399986.SZ', '银行'),
            ('399393.SZ', '地产'),
            ('399998.SZ', '科技'),
            ('399441.SZ', '医药'),
            ('399997.SZ', '消费'),
            ('399387.SZ', '制造'),
            ('399967.SZ', '军工'),
            ('399417.SZ', '新能源'),
            ('399994.SZ', '5G'),
            ('399995.SZ', '芯片')
        ]
    
        
        sector_data = []
        today = datetime.now().strftime('%Y%m%d')
        
        for code, name in sector_codes:
            try:
                # 获取板块指数数据（使用安全请求）
                df = await safe_tushare_request(pro.index_daily, ts_code=code, start_date=today, end_date=today)
                if df.empty:
                    df = await safe_tushare_request(pro.index_daily, ts_code=code, limit=1)
                
                if not df.empty:
                    row = df.iloc[0]
                    sector_data.append({
                        'name': name,
                        'code': code.split('.')[0],
                        'change': round(row['change'], 2),
                        'changePercent': round(row['pct_chg'], 2),
                        'volume': int(row['vol']),
                        'turnover': int(row['amount'] * 1000),
                        'stocks': random.randint(20, 200),  # 板块股票数量需要另外获取
                        'leadingStock': {
                            'symbol': f"00{random.randint(1, 999):04d}",
                            'name': f"{name}龙头",
                            'changePercent': round(random.uniform(-10, 15), 2)
                        }
                    })
            except Exception as e:
                print(f"获取板块 {code} 数据失败: {e}")
                # 使用模拟数据
                change = random.uniform(-5, 5)
                change_percent = change / 100 * random.uniform(80, 120)
                
                sector_data.append({
                    'name': name,
                    'code': code.split('.')[0] if '.' in code else f'BK{random.randint(100, 999)}',
                    'change': round(change, 2),
                    'changePercent': round(change_percent, 2),
                    'volume': random.randint(100000000, 1000000000),
                    'turnover': random.randint(10000000000, 100000000000),
                    'stocks': random.randint(20, 200),
                    'leadingStock': {
                        'symbol': f"00{random.randint(1, 999):04d}",
                        'name': f"{name}龙头",
                        'changePercent': round(random.uniform(-10, 15), 2)
                    }
                })
        
        return {
            'code': 200,
            'message': '获取板块数据成功',
            'data': sector_data,
            'timestamp': int(datetime.now().timestamp() * 1000)
        }
    except Exception as e:
        print(f"获取板块数据失败: {e}")
        # 返回模拟数据
        sectors = [
            {'name': '银行', 'code': 'BK001'},
            {'name': '地产', 'code': 'BK002'},
            {'name': '科技', 'code': 'BK003'},
            {'name': '医药', 'code': 'BK004'},
            {'name': '消费', 'code': 'BK005'},
            {'name': '制造', 'code': 'BK006'},
            {'name': '军工', 'code': 'BK007'},
            {'name': '新能源', 'code': 'BK008'},
            {'name': '5G', 'code': 'BK009'},
            {'name': '芯片', 'code': 'BK010'}
        ]
        
        sector_data = []
        for sector in sectors:
            change = random.uniform(-5, 5)
            change_percent = change / 100 * random.uniform(80, 120)
            
            sector_data.append({
                'name': sector['name'],
                'code': sector['code'],
                'change': round(change, 2),
                'changePercent': round(change_percent, 2),
                'volume': random.randint(100000000, 1000000000),
                'turnover': random.randint(10000000000, 100000000000),
                'stocks': random.randint(20, 200),
                'leadingStock': {
                    'symbol': f"00{random.randint(1, 999):04d}",
                    'name': f"{sector['name']}龙头",
                    'changePercent': round(random.uniform(-10, 15), 2)
                }
            })
        
        return {
            'code': 200,
            'message': '获取板块数据成功',
            'data': sector_data,
            'timestamp': int(datetime.now().timestamp() * 1000)
        }

@app.get("/api/v1/market/watchlist")
async def get_watchlist():
    """获取自选股"""
    watchlist = []
    for i in range(10):
        base_price = random.uniform(10, 100)
        change = random.uniform(-5, 5)
        change_percent = change / base_price * 100
        
        watchlist.append({
            'symbol': f"00{str(i+1).zfill(4)}",
            'name': f"自选股{i+1:02d}",
            'currentPrice': round(base_price + change, 2),
            'change': round(change, 2),
            'changePercent': round(change_percent, 2),
            'addTime': int(datetime.now().timestamp() * 1000) - random.randint(0, 30*24*3600*1000),
            'sort': i + 1
        })
    
    return {
        'code': 200,
        'message': '获取自选股成功',
        'data': watchlist,
        'timestamp': int(datetime.now().timestamp() * 1000)
    }

@app.get("/api/v1/market/indices")
async def get_market_indices():
    """获取指数数据 - 优先使用真实数据，降级到模拟数据"""
    try:
        if TUSHARE_SERVICE_AVAILABLE and tushare_service:
            # 使用Tushare服务获取真实数据
            indices_data = await tushare_service.get_indices()
            return indices_data
        elif MOCK_SERVICE_AVAILABLE and mock_market_service:
            # 使用模拟服务
            indices_data = mock_market_service.get_indices()
            return indices_data
        else:
            # 降级到静态数据
            pass
    except Exception as e:
        print(f"获取指数数据失败: {e}")
        if MOCK_SERVICE_AVAILABLE and mock_market_service:
            indices_data = mock_market_service.get_indices()
            return indices_data

    # 静态数据作为最后的降级
    indices = [
        {
            'symbol': '000001',
            'name': '上证指数',
            'currentPrice': 3234.56,
            'change': 12.34,
            'changePercent': 0.38,
            'volume': 456123456789,
            'turnover': 52345678901234
        },
        {
            'symbol': '399001',
            'name': '深证成指',
            'currentPrice': 11234.78,
            'change': -23.45,
            'changePercent': -0.21,
            'volume': 234567890123,
            'turnover': 32345678901234
        },
        {
            'symbol': '399006',
            'name': '创业板指',
            'currentPrice': 2345.67,
            'change': 5.67,
            'changePercent': 0.24,
            'volume': 123456789012,
            'turnover': 12345678901234
        },
        {
            'symbol': '000300',
            'name': '沪深300',
            'currentPrice': 4123.45,
            'change': -15.67,
            'changePercent': -0.38,
            'volume': 567890123456,
            'turnover': 62345678901234
        },
        {
            'symbol': '000016',
            'name': '上证50',
            'currentPrice': 2876.54,
            'change': 8.90,
            'changePercent': 0.31,
            'volume': 345678901234,
            'turnover': 42345678901234
        }
    ]
    
    return {
        'code': 200,
        'message': '获取指数数据成功',
        'data': indices,
        'timestamp': int(datetime.now().timestamp() * 1000)
    }

@app.get("/api/v1/market/news")
async def get_market_news(limit: int = 10, category: str = None, symbol: str = None):
    """获取新闻资讯"""
    news = []
    categories = ['市场', '公司', '政策', '行业', '国际']
    importances = ['low', 'medium', 'high']
    
    for i in range(limit):
        news.append({
            'id': f"news_{i+1:03d}",
            'title': f"重要财经新闻标题{i+1:03d}：市场动态分析",
            'summary': f"这是第{i+1}条新闻的摘要内容，包含了重要的市场信息和分析观点...",
            'content': f"详细新闻内容{i+1}...",
            'source': random.choice(['财经日报', '证券时报', '经济观察', '金融界', '同花顺']),
            'publishTime': int(datetime.now().timestamp() * 1000) - random.randint(0, 24*3600*1000),
            'tags': random.sample(['A股', '港股', '美股', '基金', '债券', '期货'], 2),
            'relatedSymbols': [f"00{random.randint(1, 999):04d}" for _ in range(random.randint(1, 3))],
            'importance': random.choice(importances),
            'url': f"https://example.com/news/{i+1}"
        })
    
    # 前端期望直接返回数组，不是包装的对象
    return news

@app.get("/api/v1/market/indices")
async def get_market_indices():
    """获取指数数据"""
    indices = [
        {
            'symbol': 'SH000001',
            'name': '上证指数',
            'currentPrice': 3234.56,
            'change': 12.34,
            'changePercent': 0.38,
            'volume': 234567890123,
            'turnover': 345678901234
        },
        {
            'symbol': 'SZ399001',
            'name': '深证成指',
            'currentPrice': 11234.78,
            'change': -23.45,
            'changePercent': -0.21,
            'volume': 123456789012,
            'turnover': 234567890123
        },
        {
            'symbol': 'SZ399006',
            'name': '创业板指',
            'currentPrice': 2345.67,
            'change': 5.67,
            'changePercent': 0.24,
            'volume': 45678901234,
            'turnover': 56789012345
        },
        {
            'symbol': 'SH000688',
            'name': '科创50',
            'currentPrice': 1056.78,
            'change': 15.23,
            'changePercent': 1.46,
            'volume': 12345678901,
            'turnover': 23456789012
        }
    ]
    
    return {
        'code': 200,
        'message': '获取指数数据成功',
        'data': indices,
        'timestamp': int(datetime.now().timestamp() * 1000)
    }

# ============ 前端期望的API路径 ============

@app.get("/market/quote")
async def get_market_quote_simple(symbols: str = None):
    """获取行情数据 - 前端期望路径"""
    if symbols:
        symbol_list = symbols.split(',')
        # 生成简化的行情数据
        quotes = []
        for symbol in symbol_list:
            base_price = random.uniform(10, 200)
            change = random.uniform(-10, 10)
            change_percent = change / base_price * 100
            quotes.append({
                'symbol': symbol.strip(),
                'name': f"股票{symbol}",
                'currentPrice': round(base_price + change, 2),
                'change': round(change, 2),
                'changePercent': round(change_percent, 2),
                'volume': random.randint(100000, 50000000),
                'turnover': random.randint(10000000, 500000000),
                'timestamp': int(datetime.now().timestamp() * 1000)
            })
        return quotes
    else:
        return []

@app.get("/market/overview")
async def get_market_overview_simple():
    """获取市场概览 - 前端期望路径"""
    # 前端期望的格式与/api/v1/market/overview一致
    overview_data = await get_market_overview()
    return overview_data

@app.get("/market/sectors")
async def get_market_sectors_simple():
    """获取板块数据 - 前端期望路径"""
    return await get_market_sectors()

@app.get("/market/indices")
async def get_market_indices_simple():
    """获取指数数据 - 前端期望路径"""
    return await get_market_indices()

@app.get("/market/news")
async def get_market_news_simple(limit: int = 10):
    """获取新闻资讯 - 前端期望路径"""
    return await get_market_news(limit)

@app.get("/market/search")
async def get_market_search_simple(keyword: str, limit: int = 10):
    """股票搜索 - 前端期望路径"""
    results = []
    for i in range(min(limit, 5)):  # 最多返回5个结果
        results.append({
            'symbol': f"{str(i+1).zfill(6)}",
            'name': f"搜索结果{i+1} - {keyword}",
            'currentPrice': round(random.uniform(10, 200), 2),
            'changePercent': round(random.uniform(-10, 10), 2),
            'market': random.choice(['SH', 'SZ'])
        })
    return {
        'code': 200,
        'data': results,
        'message': '搜索成功'
    }

@app.get("/market/kline")
async def get_market_kline_simple(symbol: str, period: str = '1d', limit: int = 100):
    """获取K线数据 - 前端期望路径"""
    klines = []
    base_price = random.uniform(50, 200)
    
    for i in range(limit):
        # 生成随机K线数据
        open_price = base_price + random.uniform(-5, 5)
        close_price = open_price + random.uniform(-10, 10)
        high_price = max(open_price, close_price) + random.uniform(0, 5)
        low_price = min(open_price, close_price) - random.uniform(0, 3)
        volume = random.randint(100000, 10000000)
        
        timestamp = int(datetime.now().timestamp() - (limit - i) * 24 * 3600) * 1000
        
        klines.append({
            'timestamp': timestamp,
            'open': round(open_price, 2),
            'high': round(high_price, 2),
            'low': round(low_price, 2),
            'close': round(close_price, 2),
            'volume': volume
        })
        
        base_price = close_price  # 下一根K线基于当前收盘价
    
    return {
        'code': 200,
        'data': klines,
        'message': '获取K线数据成功'
    }

# Mock API for frontend development
@app.get("/api/v1/backtest/mock")
async def get_backtests_mock():
    """获取回测列表(开发环境Mock数据)"""
    return {
        'success': True,
        'data': {
            'list': [
                {
                    'id': 'bt_001',
                    'name': 'MA双均线策略回测',
                    'status': 'completed',
                    'strategyId': 'stg_001',
                    'strategyName': 'MA双均线策略',
                    'symbol': '000001.SZ',
                    'startDate': '2023-01-01',
                    'endDate': '2024-01-01',
                    'initialCash': 100000,
                    'finalCash': 125000,
                    'totalReturn': 25.0,
                    'annualReturn': 25.0,
                    'maxDrawdown': 8.5,
                    'sharpeRatio': 1.85,
                    'winRate': 65.5,
                    'profitFactor': 2.1,
                    'totalTrades': 45,
                    'createdAt': '2024-01-15T10:30:00Z',
                    'updatedAt': '2024-01-15T11:45:00Z',
                    'isFavorite': True
                },
                {
                    'id': 'bt_002',
                    'name': 'MACD策略回测',
                    'status': 'running',
                    'strategyId': 'stg_002',
                    'strategyName': 'MACD策略',
                    'symbol': '000002.SZ',
                    'startDate': '2023-06-01',
                    'endDate': '2024-01-01',
                    'initialCash': 200000,
                    'progress': 65,
                    'createdAt': '2024-01-16T09:00:00Z',
                    'updatedAt': '2024-01-16T09:30:00Z',
                    'isFavorite': False
                }
            ],
            'total': 2,
            'page': 1,
            'pageSize': 20
        },
        'message': '获取回测列表成功'
    }

# ============ 策略中心API ============

@app.get("/api/v1/strategy")
async def get_strategies(
    page: int = 1,
    pageSize: int = 20,
    status: Optional[str] = None,
    type: Optional[str] = None,
    keyword: Optional[str] = None
):
    """获取策略列表"""
    strategies = []
    
    # 生成模拟策略数据
    for i in range(50):
        strategy_status = random.choice(['DRAFT', 'PUBLISHED', 'RUNNING', 'STOPPED'])
        if status and status != strategy_status:
            continue
            
        strategies.append({
            'id': f'stg_{i+1:03d}',
            'name': f'策略{i+1}: {"MA双均线" if i % 3 == 0 else "MACD动量" if i % 3 == 1 else "布林带突破"}',
            'description': f'这是策略{i+1}的详细描述，包含了策略的核心逻辑和适用场景...',
            'author': random.choice(['系统', '张三', '李四', '王五']),
            'category': random.choice(['趋势跟踪', '均值回归', '动量策略', '套利策略']),
            'type': random.choice(['股票', '期货', '期权', '数字货币']),
            'riskLevel': random.choice(['LOW', 'MEDIUM', 'HIGH']),
            'annualReturn': round(random.uniform(10, 50), 2),
            'maxDrawdown': round(random.uniform(5, 25), 2),
            'sharpeRatio': round(random.uniform(0.5, 2.5), 2),
            'winRate': round(random.uniform(40, 70), 2),
            'status': strategy_status,
            'runningDays': random.randint(0, 365) if strategy_status == 'RUNNING' else 0,
            'totalTrades': random.randint(100, 5000),
            'minCapital': random.choice([10000, 50000, 100000, 500000]),
            'maxPositions': random.randint(1, 10),
            'tags': random.sample(['高频', '低频', '日内', '趋势', '套利', '对冲'], random.randint(1, 3)),
            'rating': round(random.uniform(3, 5), 1),
            'subscribers': random.randint(0, 1000),
            'createdAt': datetime.now() - timedelta(days=random.randint(1, 365)),
            'updatedAt': datetime.now() - timedelta(days=random.randint(0, 30)),
            'isFavorite': random.choice([True, False]),
            'isPublic': random.choice([True, False])
        })
    
    # 分页
    start = (page - 1) * pageSize
    end = start + pageSize
    paginated_strategies = strategies[start:end]
    
    return {
        'code': 200,
        'message': '获取策略列表成功',
        'data': {
            'items': paginated_strategies,
            'total': len(strategies),
            'page': page,
            'pageSize': pageSize,
            'hasMore': end < len(strategies)
        }
    }

@app.get("/api/v1/strategy/{strategy_id}")
async def get_strategy_detail(strategy_id: str):
    """获取策略详情"""
    return {
        'code': 200,
        'message': '获取策略详情成功',
        'data': {
            'id': strategy_id,
            'name': 'MA双均线策略',
            'description': '基于移动平均线的经典趋势跟踪策略，适合中长期投资',
            'author': '系统',
            'category': '趋势跟踪',
            'type': '股票',
            'riskLevel': 'MEDIUM',
            'annualReturn': 25.6,
            'maxDrawdown': 12.3,
            'sharpeRatio': 1.85,
            'winRate': 58.5,
            'status': 'RUNNING',
            'runningDays': 120,
            'totalTrades': 856,
            'minCapital': 100000,
            'maxPositions': 5,
            'tags': ['趋势', '中频', '稳健'],
            'code': '''
# MA双均线策略
def initialize(context):
    context.security = '000001.SZ'
    context.short_period = 5
    context.long_period = 20

def handle_data(context, data):
    security = context.security
    short_ma = data[security].mavg(context.short_period)
    long_ma = data[security].mavg(context.long_period)
    
    if short_ma > long_ma and context.portfolio.positions[security].quantity == 0:
        order_target_percent(security, 1.0)
    elif short_ma < long_ma and context.portfolio.positions[security].quantity > 0:
        order_target_percent(security, 0)
            ''',
            'parameters': [
                {'name': 'short_period', 'type': 'int', 'default': 5, 'min': 1, 'max': 50, 'description': '短期均线周期'},
                {'name': 'long_period', 'type': 'int', 'default': 20, 'min': 10, 'max': 200, 'description': '长期均线周期'}
            ],
            'performance': {
                'totalReturn': 156.8,
                'annualReturn': 25.6,
                'monthlyReturn': 2.1,
                'weeklyReturn': 0.5,
                'maxDrawdown': 12.3,
                'sharpeRatio': 1.85,
                'sortinoRatio': 2.15,
                'calmarRatio': 2.08,
                'winRate': 58.5,
                'profitFactor': 1.68,
                'totalTrades': 856,
                'winningTrades': 501,
                'losingTrades': 355,
                'avgWin': 3.2,
                'avgLoss': -1.9,
                'maxConsecutiveWins': 12,
                'maxConsecutiveLosses': 7
            }
        }
    }

@app.post("/api/v1/strategy/{strategy_id}/start")
async def start_strategy(strategy_id: str):
    """启动策略"""
    return {
        'code': 200,
        'message': '策略启动成功',
        'data': {
            'id': strategy_id,
            'status': 'RUNNING',
            'startTime': datetime.now().isoformat()
        }
    }

@app.post("/api/v1/strategy/{strategy_id}/stop")
async def stop_strategy(strategy_id: str):
    """停止策略"""
    return {
        'code': 200,
        'message': '策略停止成功',
        'data': {
            'id': strategy_id,
            'status': 'STOPPED',
            'stopTime': datetime.now().isoformat()
        }
    }

@app.get("/api/v1/strategy/{strategy_id}/performance")
async def get_strategy_performance(strategy_id: str):
    """获取策略性能"""
    return {
        'code': 200,
        'message': '获取策略性能成功',
        'data': {
            'strategyId': strategy_id,
            'metrics': {
                'totalReturn': round(random.uniform(50, 200), 2),
                'annualReturn': round(random.uniform(15, 40), 2),
                'sharpeRatio': round(random.uniform(1, 2.5), 2),
                'maxDrawdown': round(random.uniform(5, 20), 2),
                'winRate': round(random.uniform(45, 65), 2)
            },
            'equityCurve': [
                {'date': (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d'), 
                 'value': 100000 * (1 + random.uniform(-0.02, 0.03) * i)}
                for i in range(30, 0, -1)
            ]
        }
    }

@app.get("/api/v1/strategy/templates")
async def get_strategy_templates():
    """获取策略模板"""
    return {
        'code': 200,
        'message': '获取策略模板成功',
        'data': [
            {
                'id': 'tpl_001',
                'name': 'MA双均线模板',
                'description': '经典的移动平均线交叉策略模板',
                'category': '趋势跟踪',
                'difficulty': 'EASY',
                'code': '# MA strategy template code...'
            },
            {
                'id': 'tpl_002',
                'name': 'MACD策略模板',
                'description': '基于MACD指标的动量策略模板',
                'category': '动量策略',
                'difficulty': 'MEDIUM',
                'code': '# MACD strategy template code...'
            },
            {
                'id': 'tpl_003',
                'name': '网格交易模板',
                'description': '适合震荡市的网格交易策略模板',
                'category': '套利策略',
                'difficulty': 'HARD',
                'code': '# Grid trading template code...'
            }
        ]
    }

@app.get("/api/v1/strategy/featured")
async def get_featured_strategies():
    """获取推荐策略"""
    return {
        'code': 200,
        'message': '获取推荐策略成功',
        'data': [
            {
                'id': 'stg_001',
                'name': '稳健增长策略',
                'description': '低风险稳健收益策略',
                'annualReturn': 18.5,
                'sharpeRatio': 2.1,
                'subscribers': 1250,
                'rating': 4.8
            },
            {
                'id': 'stg_002',
                'name': '高频套利策略',
                'description': '高频交易套利策略',
                'annualReturn': 35.2,
                'sharpeRatio': 1.8,
                'subscribers': 856,
                'rating': 4.6
            }
        ]
    }

# 注释掉可能的导入错误
# from backtest_api import router as backtest_router
# app.include_router(backtest_router, prefix="/api/backtest", tags=["回测"])

# ============ 兼容前端路由 (不带/api前缀) ============

# 行情中心路由兼容
@app.get("/v1/market/overview")
async def get_market_overview_compat():
    return await get_market_overview()

@app.get("/v1/market/stocks")
async def get_market_stocks_compat(market: str = None, industry: str = None, page: int = 1, pageSize: int = 50):
    return await get_market_stocks(market, industry, page, pageSize)

@app.get("/v1/market/overview/ranking")
async def get_market_ranking_compat(type: str = "change_percent", limit: int = 50, market: str = None, direction: str = "desc"):
    return await get_market_ranking(type, limit, market, direction)

@app.get("/v1/market/sectors")
async def get_market_sectors_compat():
    return await get_market_sectors()

@app.get("/v1/market/watchlist")
async def get_watchlist_compat():
    return await get_watchlist()

@app.get("/v1/market/news")
async def get_market_news_compat(limit: int = 10, category: str = None, symbol: str = None):
    return await get_market_news(limit, category, symbol)

@app.get("/v1/market/indices") 
async def get_market_indices_compat():
    """获取指数数据 - 兼容路由"""
    return {
        'code': 200,
        'message': '获取指数数据成功',
        'data': {
            'indices': [
                {
                    'symbol': 'SH000001',
                    'name': '上证指数',
                    'currentPrice': 3234.56,
                    'change': 12.34,
                    'changePercent': 0.38,
                    'volume': 234567890000,
                    'turnover': 345678901234
                },
                {
                    'symbol': 'SZ399001',
                    'name': '深证成指',
                    'currentPrice': 11234.78,
                    'change': -23.45,
                    'changePercent': -0.21,
                    'volume': 123456780000,
                    'turnover': 234567890123
                },
                {
                    'symbol': 'SZ399006',
                    'name': '创业板指',
                    'currentPrice': 2345.67,
                    'change': 5.67,
                    'changePercent': 0.24,
                    'volume': 45678900000,
                    'turnover': 56789012345
                }
            ]
        },
        'timestamp': int(datetime.now().timestamp() * 1000)
    }

# ============ 补充缺失的市场数据API ============

@app.get("/api/v1/market/quote")
async def get_market_quote_v1(symbol: str):
    """获取实时行情"""
    price = random.uniform(10, 200)
    change = random.uniform(-10, 10)
    return {
        'code': 200,
        'message': '获取行情成功',
        'data': {
            'symbol': symbol,
            'name': f'股票{symbol[-3:]}',
            'currentPrice': round(price, 2),
            'previousClose': round(price - change, 2),
            'change': round(change, 2),
            'changePercent': round(change / price * 100, 2),
            'high': round(price + abs(change) + random.uniform(0, 5), 2),
            'low': round(price - abs(change) - random.uniform(0, 2), 2),
            'openPrice': round(price + random.uniform(-2, 2), 2),
            'volume': random.randint(100000, 50000000),
            'turnover': random.randint(10000000, 500000000),
            'timestamp': int(datetime.now().timestamp() * 1000),
            'status': 'trading'
        },
        'timestamp': int(datetime.now().timestamp() * 1000)
    }

@app.get("/api/v1/market/kline")
async def get_market_kline_v1(symbol: str, interval: str = "1d", limit: int = 100):
    """获取K线数据"""
    klines = []
    base_price = random.uniform(50, 150)
    current_time = datetime.now()
    
    for i in range(limit):
        time_delta = timedelta(days=limit-i-1) if interval == "1d" else timedelta(minutes=(limit-i-1)*5)
        timestamp = int((current_time - time_delta).timestamp() * 1000)
        
        open_price = base_price + random.uniform(-5, 5)
        close_price = open_price + random.uniform(-3, 3)
        high_price = max(open_price, close_price) + random.uniform(0, 2)
        low_price = min(open_price, close_price) - random.uniform(0, 2)
        
        klines.append({
            'timestamp': timestamp,
            'open': round(open_price, 2),
            'high': round(high_price, 2),
            'low': round(low_price, 2),
            'close': round(close_price, 2),
            'volume': random.randint(100000, 10000000),
            'turnover': random.randint(10000000, 1000000000)
        })
        
        base_price = close_price
    
    return {
        'code': 200,
        'message': '获取K线数据成功',
        'data': klines,
        'timestamp': int(datetime.now().timestamp() * 1000)
    }

@app.get("/api/v1/market/search")
async def search_stocks_v1(keyword: str, limit: int = 20):
    """搜索股票"""
    results = []
    for i in range(min(limit, 10)):
        symbol = f"00{random.randint(1, 999):04d}"
        results.append({
            'symbol': symbol,
            'name': f'{keyword}相关{i+1}',
            'market': random.choice(['SH', 'SZ']),
            'industry': random.choice(['银行', '地产', '科技', '医药'])
        })
    
    return {
        'code': 200,
        'message': '搜索成功',
        'data': results,
        'timestamp': int(datetime.now().timestamp() * 1000)
    }

# ============ 补充缺失的用户API ============

@app.get("/api/v1/user/profile")
async def get_user_profile():
    """获取用户信息"""
    return {
        'code': 200,
        'message': '获取用户信息成功',
        'data': {
            'id': 'user_001',
            'username': 'test_user',
            'email': '<EMAIL>',
            'nickname': '测试用户',
            'avatar': 'https://via.placeholder.com/100',
            'createdAt': '2024-01-01T00:00:00Z',
            'role': 'user',
            'status': 'active'
        },
        'timestamp': int(datetime.now().timestamp() * 1000)
    }

@app.put("/api/v1/user/profile") 
async def update_user_profile(data: dict):
    """更新用户信息"""
    return {
        'code': 200,
        'message': '更新用户信息成功',
        'data': {
            'id': 'user_001',
            'username': 'test_user',
            'email': data.get('email', '<EMAIL>'),
            'nickname': data.get('nickname', '测试用户'),
            'avatar': data.get('avatar', 'https://via.placeholder.com/100'),
            'updatedAt': datetime.now().isoformat()
        },
        'timestamp': int(datetime.now().timestamp() * 1000)
    }

# ============ 策略文件管理API ============

from pathlib import Path

# 策略文件基础路径
STRATEGY_BASE_PATH = Path(__file__).parent.parent / "data" / "Strategy"

def extract_strategy_info(content: str) -> Dict[str, str]:
    """从策略内容中提取信息"""
    info = {}
    lines = content.split('\n')

    for line in lines[:20]:  # 只检查前20行
        line = line.strip()
        if line.startswith('#'):
            if '标题：' in line or '标题:' in line:
                info['title'] = line.split('：')[-1].split(':')[-1].strip()
            elif '作者：' in line or '作者:' in line:
                info['author'] = line.split('：')[-1].split(':')[-1].strip()
            elif 'http' in line:
                info['source_url'] = line.replace('#', '').strip()

    return info

@app.get("/api/v1/strategy-files/years")
async def get_available_years():
    """获取可用年份列表"""
    try:
        if not STRATEGY_BASE_PATH.exists():
            return {
                "years": [],
                "total": 0,
                "message": "策略目录不存在"
            }

        years = []
        for item in STRATEGY_BASE_PATH.iterdir():
            if item.is_dir() and item.name.isdigit():
                years.append(item.name)

        years.sort()
        return {
            "years": years,
            "total": len(years),
            "message": "获取年份列表成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取年份列表失败: {str(e)}")

@app.get("/api/v1/strategy-files/{year}")
async def get_files_by_year(year: str):
    """获取指定年份的策略文件列表"""
    try:
        year_path = STRATEGY_BASE_PATH / year
        if not year_path.exists():
            raise HTTPException(status_code=404, detail=f"年份目录不存在: {year}")

        files = []
        for file_path in year_path.glob("*.txt"):
            files.append(file_path.name)

        files.sort()
        return {
            "files": files,
            "total": len(files),
            "year": year
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文件列表失败: {str(e)}")

@app.get("/api/v1/strategy-files/{year}/{filename}")
async def get_file_detail(year: str, filename: str):
    """获取策略文件详情"""
    try:
        file_path = STRATEGY_BASE_PATH / year / filename
        if not file_path.exists():
            raise HTTPException(status_code=404, detail=f"文件不存在: {year}/{filename}")

        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 提取策略信息
        strategy_info = extract_strategy_info(content)

        return {
            "year": year,
            "filename": filename,
            "title": strategy_info.get('title', filename.replace('.txt', '')),
            "author": strategy_info.get('author', '未知'),
            "source_url": strategy_info.get('source_url', ''),
            "description": strategy_info.get('description', ''),
            "content": content,
            "content_length": len(content),
            "line_count": len(content.split('\n'))
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文件详情失败: {str(e)}")

@app.get("/api/v1/strategy-files/search")
async def search_strategies(keyword: str = Query(..., description="搜索关键词")):
    """搜索策略文件"""
    try:
        if not STRATEGY_BASE_PATH.exists():
            return []

        results = []
        keyword_lower = keyword.lower()

        for year_dir in STRATEGY_BASE_PATH.iterdir():
            if not year_dir.is_dir() or not year_dir.name.isdigit():
                continue

            for file_path in year_dir.glob("*.txt"):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # 检查文件名、内容是否包含关键词
                    if (keyword_lower in file_path.name.lower() or
                        keyword_lower in content.lower()):

                        strategy_info = extract_strategy_info(content)
                        results.append({
                            "year": year_dir.name,
                            "filename": file_path.name,
                            "title": strategy_info.get('title', file_path.name.replace('.txt', '')),
                            "author": strategy_info.get('author', '未知'),
                            "description": strategy_info.get('description', '')
                        })
                except:
                    continue

        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@app.post("/api/v1/strategy-files/{year}/{filename}/import")
async def import_strategy_file(year: str, filename: str, strategy_name: Optional[str] = None):
    """导入策略文件到用户策略库"""
    try:
        file_path = STRATEGY_BASE_PATH / year / filename
        if not file_path.exists():
            raise HTTPException(status_code=404, detail=f"文件不存在: {year}/{filename}")

        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        strategy_info = extract_strategy_info(content)
        final_name = strategy_name or strategy_info.get('title', filename.replace('.txt', ''))

        # 这里可以添加实际的导入逻辑，比如保存到用户策略库
        # 目前只是模拟成功

        return {
            "success": True,
            "strategy_id": f"imported_{year}_{filename}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "message": f"策略 '{final_name}' 导入成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导入失败: {str(e)}")

if __name__ == "__main__":
    print("[启动] 量化交易平台后端服务...")
    uvicorn.run(
        "simple_main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
