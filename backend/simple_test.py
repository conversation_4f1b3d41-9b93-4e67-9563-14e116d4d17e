#!/usr/bin/env python3
"""
简化的回测系统测试脚本
"""
import asyncio
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


async def test_dependencies():
    """测试依赖包安装情况"""
    logger.info("🧪 测试依赖包...")
    
    results = {}
    
    # 测试pandas
    try:
        import pandas as pd
        results['pandas'] = f"✅ v{pd.__version__}"
    except ImportError:
        results['pandas'] = "❌ 未安装"
    
    # 测试numpy
    try:
        import numpy as np
        results['numpy'] = f"✅ v{np.__version__}"
    except ImportError:
        results['numpy'] = "❌ 未安装"
    
    # 测试pyecharts
    try:
        import pyecharts
        results['pyecharts'] = f"✅ v{pyecharts.__version__}"
    except ImportError:
        results['pyecharts'] = "❌ 未安装"
    
    # 测试akshare
    try:
        import akshare as ak
        results['akshare'] = "✅ 已安装"
    except ImportError:
        results['akshare'] = "❌ 未安装"
    
    # 输出结果
    for package, status in results.items():
        logger.info(f"   {package}: {status}")
    
    return results


if __name__ == "__main__":
    asyncio.run(test_dependencies())