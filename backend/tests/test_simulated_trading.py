"""
模拟交易系统测试脚本
"""
import asyncio
import aiohttp
import json
from datetime import datetime


class SimulatedTradingTester:
    """模拟交易测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.token = None
        self.account_id = None
        
    async def login(self, username: str = "testuser", password: str = "testpass123"):
        """登录获取token"""
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/api/v1/auth/login",
                json={"username": username, "password": password}
            ) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    self.token = data['data']['access_token']
                    print(f"✓ 登录成功，获取token")
                    return True
                else:
                    print(f"✗ 登录失败: {await resp.text()}")
                    return False
                    
    def get_headers(self):
        """获取请求头"""
        return {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json"
        }
        
    async def test_create_simulated_account(self):
        """测试创建模拟账户"""
        print("\n1. 测试创建模拟账户")
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/api/v1/simulated/accounts/create",
                headers=self.get_headers(),
                json={
                    "initial_capital": 1000000,
                    "reason": "USER_CREATE",
                    "settings": {
                        "slippage_mode": "RANDOM",
                        "execution_delay": 300
                    }
                }
            ) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    self.account_id = data['data']['account_id']
                    print(f"✓ 创建模拟账户成功: {self.account_id}")
                    print(f"  初始资金: {data['data']['initial_capital']}")
                    return True
                else:
                    print(f"✗ 创建失败: {await resp.text()}")
                    return False
                    
    async def test_get_simulated_accounts(self):
        """测试获取模拟账户列表"""
        print("\n2. 测试获取模拟账户列表")
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{self.base_url}/api/v1/simulated/accounts",
                headers=self.get_headers()
            ) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    accounts = data['data']
                    print(f"✓ 获取账户列表成功，共 {len(accounts)} 个账户")
                    for acc in accounts:
                        print(f"  - {acc['account_id']}: 资产 {acc['total_assets']}")
                    return True
                else:
                    print(f"✗ 获取失败: {await resp.text()}")
                    return False
                    
    async def test_switch_account(self):
        """测试切换账户"""
        print("\n3. 测试切换账户")
        # 切换到模拟账户
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/api/v1/simulated/accounts/switch/SIMULATED",
                headers=self.get_headers()
            ) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✓ 切换到模拟账户成功: {data['data']['account_id']}")
                    return True
                else:
                    print(f"✗ 切换失败: {await resp.text()}")
                    return False
                    
    async def test_create_order(self):
        """测试创建模拟订单"""
        print("\n4. 测试创建模拟订单")
        async with aiohttp.ClientSession() as session:
            # 创建买入订单
            async with session.post(
                f"{self.base_url}/api/v1/simulated/orders",
                headers=self.get_headers(),
                json={
                    "symbol": "000001",
                    "exchange": "SSE",
                    "direction": "BUY",
                    "order_type": "LIMIT",
                    "price": 10.50,
                    "volume": 1000
                }
            ) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✓ 创建买入订单成功")
                    print(f"  订单ID: {data['data']['order_id']}")
                    if data['data']['success']:
                        print(f"  成交ID: {data['data'].get('trade_id')}")
                    return True
                else:
                    print(f"✗ 创建失败: {await resp.text()}")
                    return False
                    
    async def test_get_current_account(self):
        """测试获取当前账户信息"""
        print("\n5. 测试获取当前账户信息")
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{self.base_url}/api/v1/simulated/current-account",
                headers=self.get_headers()
            ) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    acc = data['data']
                    print(f"✓ 获取当前账户成功")
                    print(f"  账户ID: {acc['account_id']}")
                    print(f"  账户类型: {acc['account_type']}")
                    print(f"  总资产: {acc['total_assets']}")
                    print(f"  可用资金: {acc['available_cash']}")
                    print(f"  持仓市值: {acc['market_value']}")
                    return True
                else:
                    print(f"✗ 获取失败: {await resp.text()}")
                    return False
                    
    async def test_get_performance(self):
        """测试获取账户业绩"""
        print("\n6. 测试获取账户业绩")
        if not self.account_id:
            print("✗ 需要先创建账户")
            return False
            
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{self.base_url}/api/v1/simulated/performance/{self.account_id}?period=1M",
                headers=self.get_headers()
            ) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    perf = data['data']
                    print(f"✓ 获取业绩成功")
                    print(f"  总收益率: {perf['performance']['total_return']}%")
                    print(f"  最大回撤: {perf['performance']['max_drawdown']}%")
                    print(f"  夏普比率: {perf['performance']['sharpe_ratio']}")
                    return True
                else:
                    print(f"✗ 获取失败: {await resp.text()}")
                    return False
                    
    async def test_get_guide(self):
        """测试获取模拟交易指南"""
        print("\n7. 测试获取模拟交易指南")
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{self.base_url}/api/v1/simulated/guide",
                headers=self.get_headers()
            ) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    guide = data['data']
                    print(f"✓ 获取指南成功")
                    print(f"  介绍: {guide['introduction']}")
                    print(f"  功能特性: {len(guide['features'])} 项")
                    return True
                else:
                    print(f"✗ 获取失败: {await resp.text()}")
                    return False
                    
    async def run_all_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("模拟交易系统测试")
        print("=" * 60)
        
        # 登录
        if not await self.login():
            print("登录失败，测试终止")
            return
            
        # 运行测试
        tests = [
            self.test_create_simulated_account,
            self.test_get_simulated_accounts,
            self.test_switch_account,
            self.test_create_order,
            self.test_get_current_account,
            self.test_get_performance,
            self.test_get_guide
        ]
        
        success_count = 0
        for test in tests:
            try:
                if await test():
                    success_count += 1
            except Exception as e:
                print(f"✗ 测试异常: {str(e)}")
                
        print("\n" + "=" * 60)
        print(f"测试完成: {success_count}/{len(tests)} 成功")
        print("=" * 60)


async def main():
    """主函数"""
    tester = SimulatedTradingTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())