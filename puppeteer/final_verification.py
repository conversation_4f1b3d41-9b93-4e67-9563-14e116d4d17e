#!/usr/bin/env python3
"""
最终验证脚本 - 确保 MCP Puppeteer 服务器完全正常工作
"""

import json
import sys
import subprocess
import time
import os
from pathlib import Path

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🔍 {title}")
    print("=" * 60)

def print_success(message):
    """打印成功消息"""
    print(f"✅ {message}")

def print_error(message):
    """打印错误消息"""
    print(f"❌ {message}")

def print_info(message):
    """打印信息消息"""
    print(f"ℹ️  {message}")

def check_environment():
    """检查环境配置"""
    print_header("环境检查")
    
    # 检查虚拟环境
    if 'venv' in sys.executable:
        print_success("虚拟环境已激活")
    else:
        print_error("虚拟环境未激活")
        return False
    
    # 检查必要的包
    try:
        import mcp
        print_success("MCP 库已安装")
    except ImportError:
        print_error("MCP 库未安装")
        return False
    
    try:
        import playwright
        print_success("Playwright 库已安装")
    except ImportError:
        print_error("Playwright 库未安装")
        return False
    
    # 检查浏览器
    try:
        from playwright.sync_api import sync_playwright
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            browser.close()
        print_success("Chromium 浏览器可用")
    except Exception as e:
        print_error(f"Chromium 浏览器不可用: {e}")
        return False
    
    return True

def test_mcp_server():
    """测试 MCP 服务器"""
    print_header("MCP 服务器测试")
    
    try:
        # 启动服务器
        process = subprocess.Popen(
            [sys.executable, "puppeteer.py"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=0
        )
        
        time.sleep(2)
        
        # 初始化
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {"roots": {"listChanged": True}, "sampling": {}},
                "clientInfo": {"name": "verification-client", "version": "1.0.0"}
            }
        }
        
        process.stdin.write(json.dumps(init_request) + "\n")
        process.stdin.flush()
        
        response = process.stdout.readline()
        if response:
            result = json.loads(response.strip())
            if "result" in result:
                print_success("服务器初始化成功")
            else:
                print_error("服务器初始化失败")
                return False
        
        # 发送 initialized 通知
        initialized = {"jsonrpc": "2.0", "method": "notifications/initialized"}
        process.stdin.write(json.dumps(initialized) + "\n")
        process.stdin.flush()
        
        # 获取工具列表
        tools_request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/list",
            "params": {}
        }
        
        process.stdin.write(json.dumps(tools_request) + "\n")
        process.stdin.flush()
        
        response = process.stdout.readline()
        if response:
            result = json.loads(response.strip())
            if "result" in result and "tools" in result["result"]:
                tools = result["result"]["tools"]
                print_success(f"获取到 {len(tools)} 个工具")
                for tool in tools:
                    print_info(f"  - {tool['name']}")
            else:
                print_error("获取工具列表失败")
                return False
        
        process.terminate()
        return True
        
    except Exception as e:
        print_error(f"MCP 服务器测试失败: {e}")
        if process:
            process.terminate()
        return False

def test_browser_functionality():
    """测试浏览器功能"""
    print_header("浏览器功能测试")
    
    try:
        from playwright.sync_api import sync_playwright
        
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            
            # 测试导航
            page.goto("https://httpbin.org/html")
            print_success("网页导航成功")
            
            # 测试 JavaScript 执行
            title = page.evaluate("document.title")
            print_success(f"JavaScript 执行成功，页面标题: '{title}'")
            
            # 测试元素查找
            h1_exists = page.query_selector("h1") is not None
            if h1_exists:
                print_success("元素查找功能正常")
            else:
                print_error("元素查找功能异常")
            
            browser.close()
            return True
            
    except Exception as e:
        print_error(f"浏览器功能测试失败: {e}")
        return False

def check_files():
    """检查文件完整性"""
    print_header("文件完整性检查")
    
    required_files = [
        "puppeteer.py",
        "requirements.txt",
        "test_mcp.py",
        "test_mcp_simple.py",
        "example_usage.py",
        "start_server.sh",
        "SETUP_COMPLETE.md"
    ]
    
    all_present = True
    for file in required_files:
        if os.path.exists(file):
            print_success(f"{file} 存在")
        else:
            print_error(f"{file} 缺失")
            all_present = False
    
    return all_present

def main():
    """主函数"""
    print_header("MCP Puppeteer 服务器最终验证")
    print_info("正在进行全面的功能验证...")
    
    # 确保在正确的目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    tests = [
        ("环境配置", check_environment),
        ("文件完整性", check_files),
        ("MCP 服务器", test_mcp_server),
        ("浏览器功能", test_browser_functionality)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print_error(f"{test_name} 测试失败")
        except Exception as e:
            print_error(f"{test_name} 测试出错: {e}")
    
    print_header("验证结果")
    print(f"📊 通过: {passed}/{total}")
    
    if passed == total:
        print_success("🎉 所有验证通过！MCP Puppeteer 服务器完全就绪！")
        print_info("您现在可以开始使用所有功能了。")
        return True
    else:
        print_error("💥 部分验证失败，请检查上述错误信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
