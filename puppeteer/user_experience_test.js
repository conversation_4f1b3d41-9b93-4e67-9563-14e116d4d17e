/**
 * 用户体验深度测试
 * 模拟真实用户使用量化投资平台的完整流程
 */

const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs').promises;
const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);

class UserExperienceTest {
    constructor() {
        this.browser = null;
        this.page = null;
        this.baseUrl = 'http://localhost:5173'; // 前端地址
        this.apiUrl = 'http://localhost:8000'; // 后端地址
        this.testResults = [];
        this.screenshots = [];
        this.testUser = {
            username: 'test_user_' + Date.now(),
            password: 'Test123456!',
            email: `test_${Date.now()}@example.com`
        };
    }

    async init() {
        console.log('🚀 启动浏览器...');
        this.browser = await puppeteer.launch({
            headless: false, // 显示浏览器界面
            defaultViewport: { width: 1920, height: 1080 },
            args: ['--no-sandbox', '--disable-setuid-sandbox'],
            slowMo: 50 // 放慢操作速度，便于观察
        });
        
        this.page = await this.browser.newPage();
        
        // 监听控制台消息
        this.page.on('console', msg => {
            if (msg.type() === 'error') {
                this.logIssue('控制台错误', msg.text(), 'high');
            }
        });
        
        // 监听页面错误
        this.page.on('pageerror', error => {
            this.logIssue('页面错误', error.message, 'high');
        });
        
        // 监听请求失败
        this.page.on('requestfailed', request => {
            this.logIssue('请求失败', `${request.url()} - ${request.failure().errorText}`, 'medium');
        });
    }

    async takeScreenshot(name) {
        const filename = `screenshots/ux_test_${name}_${Date.now()}.png`;
        await this.page.screenshot({ path: filename, fullPage: true });
        this.screenshots.push(filename);
        console.log(`📸 截图已保存: ${filename}`);
    }

    logIssue(category, description, severity = 'medium') {
        const issue = {
            category,
            description,
            severity,
            timestamp: new Date().toISOString(),
            url: this.page.url()
        };
        this.testResults.push(issue);
        console.log(`❗ ${severity.toUpperCase()} - ${category}: ${description}`);
    }

    async testUserJourney() {
        console.log('\n🧪 开始用户体验测试...\n');

        // 1. 访问首页
        await this.testHomePage();
        
        // 2. 注册新用户
        await this.testRegistration();
        
        // 3. 登录系统
        await this.testLogin();
        
        // 4. 浏览市场行情
        await this.testMarketData();
        
        // 5. 使用交易终端
        await this.testTradingTerminal();
        
        // 6. 管理订单
        await this.testOrderManagement();
        
        // 7. 开发策略
        await this.testStrategyDevelopment();
        
        // 8. 查看持仓和账户
        await this.testPortfolio();
        
        // 9. 风险管理
        await this.testRiskManagement();
        
        // 10. 系统设置
        await this.testSettings();
    }

    async testHomePage() {
        console.log('📍 测试首页...');
        
        try {
            await this.page.goto(this.baseUrl, { waitUntil: 'networkidle0' });
            await this.takeScreenshot('homepage');
            
            // 检查页面加载
            const title = await this.page.title();
            if (!title) {
                this.logIssue('首页', '页面标题为空', 'low');
            }
            
            // 检查导航菜单
            const navItems = await this.page.$$('.nav-menu-item');
            if (navItems.length === 0) {
                this.logIssue('首页', '导航菜单未找到', 'high');
            }
            
            // 检查响应式设计
            await this.page.setViewport({ width: 768, height: 1024 });
            await this.page.waitForTimeout(1000);
            await this.takeScreenshot('homepage_mobile');
            
            // 恢复正常视口
            await this.page.setViewport({ width: 1920, height: 1080 });
            
        } catch (error) {
            this.logIssue('首页', `加载失败: ${error.message}`, 'critical');
        }
    }

    async testRegistration() {
        console.log('📍 测试用户注册...');
        
        try {
            // 点击注册按钮
            const registerBtn = await this.page.$('[data-testid="register-btn"], a[href*="register"], button:has-text("注册")');
            if (!registerBtn) {
                this.logIssue('注册', '找不到注册按钮', 'high');
                return;
            }
            
            await registerBtn.click();
            await this.page.waitForTimeout(2000);
            
            // 填写注册表单
            await this.page.type('input[name="username"], input[placeholder*="用户名"]', this.testUser.username);
            await this.page.type('input[name="email"], input[placeholder*="邮箱"]', this.testUser.email);
            await this.page.type('input[name="password"], input[placeholder*="密码"]', this.testUser.password);
            
            // 确认密码
            const confirmPwd = await this.page.$('input[name="confirmPassword"], input[placeholder*="确认密码"]');
            if (confirmPwd) {
                await confirmPwd.type(this.testUser.password);
            }
            
            await this.takeScreenshot('registration_form');
            
            // 提交注册
            const submitBtn = await this.page.$('button[type="submit"], button:has-text("注册")');
            if (submitBtn) {
                await submitBtn.click();
                await this.page.waitForTimeout(3000);
                
                // 检查注册结果
                const errorMsg = await this.page.$('.error-message, .el-message--error');
                if (errorMsg) {
                    const text = await errorMsg.textContent();
                    this.logIssue('注册', `注册失败: ${text}`, 'high');
                }
            }
            
        } catch (error) {
            this.logIssue('注册', `注册流程错误: ${error.message}`, 'high');
        }
    }

    async testLogin() {
        console.log('📍 测试用户登录...');
        
        try {
            // 如果不在登录页，先导航到登录页
            if (!this.page.url().includes('login')) {
                await this.page.goto(`${this.baseUrl}/login`, { waitUntil: 'networkidle0' });
            }
            
            await this.page.waitForTimeout(2000);
            
            // 清空输入框并输入登录信息
            const usernameInput = await this.page.$('input[name="username"], input[placeholder*="用户名"]');
            const passwordInput = await this.page.$('input[name="password"], input[placeholder*="密码"]');
            
            if (usernameInput && passwordInput) {
                await usernameInput.click({ clickCount: 3 });
                await usernameInput.type(this.testUser.username);
                
                await passwordInput.click({ clickCount: 3 });
                await passwordInput.type(this.testUser.password);
                
                await this.takeScreenshot('login_form');
                
                // 提交登录
                const loginBtn = await this.page.$('button[type="submit"], button:has-text("登录")');
                if (loginBtn) {
                    await loginBtn.click();
                    await this.page.waitForTimeout(3000);
                    
                    // 检查登录是否成功
                    if (this.page.url().includes('login')) {
                        this.logIssue('登录', '登录后仍在登录页面', 'high');
                    }
                }
            } else {
                this.logIssue('登录', '找不到登录表单输入框', 'critical');
            }
            
        } catch (error) {
            this.logIssue('登录', `登录流程错误: ${error.message}`, 'critical');
        }
    }

    async testMarketData() {
        console.log('📍 测试市场行情...');
        
        try {
            // 导航到市场数据页面
            await this.navigateToSection('市场', 'market');
            await this.page.waitForTimeout(3000);
            await this.takeScreenshot('market_data');
            
            // 检查股票列表
            const stockList = await this.page.$$('.stock-item, .market-table-row, tbody tr');
            if (stockList.length === 0) {
                this.logIssue('市场数据', '股票列表为空', 'high');
            } else {
                console.log(`✅ 找到 ${stockList.length} 只股票`);
                
                // 点击第一只股票查看详情
                if (stockList[0]) {
                    await stockList[0].click();
                    await this.page.waitForTimeout(2000);
                    
                    // 检查K线图
                    const chart = await this.page.$('.kline-chart, .chart-container, canvas');
                    if (!chart) {
                        this.logIssue('市场数据', 'K线图未加载', 'medium');
                    }
                    
                    // 检查实时行情更新
                    const priceElement = await this.page.$('.current-price, .price');
                    if (priceElement) {
                        const initialPrice = await priceElement.textContent();
                        await this.page.waitForTimeout(5000);
                        const updatedPrice = await priceElement.textContent();
                        
                        if (initialPrice === updatedPrice) {
                            this.logIssue('市场数据', '价格5秒内未更新', 'low');
                        }
                    }
                }
            }
            
            // 测试搜索功能
            const searchInput = await this.page.$('input[placeholder*="搜索"], .search-input');
            if (searchInput) {
                await searchInput.type('平安');
                await this.page.waitForTimeout(1000);
                
                const searchResults = await this.page.$$('.search-result, .suggestion-item');
                if (searchResults.length === 0) {
                    this.logIssue('市场数据', '搜索功能无结果', 'medium');
                }
            }
            
        } catch (error) {
            this.logIssue('市场数据', `市场数据测试失败: ${error.message}`, 'high');
        }
    }

    async testTradingTerminal() {
        console.log('📍 测试交易终端...');
        
        try {
            await this.navigateToSection('交易', 'trading');
            await this.page.waitForTimeout(3000);
            await this.takeScreenshot('trading_terminal');
            
            // 检查交易界面元素
            const elements = {
                '买入按钮': '.buy-btn, button:has-text("买入")',
                '卖出按钮': '.sell-btn, button:has-text("卖出")',
                '价格输入': 'input[name="price"], input[placeholder*="价格"]',
                '数量输入': 'input[name="quantity"], input[placeholder*="数量"]',
                '委托列表': '.order-list, .pending-orders',
                '持仓列表': '.position-list, .positions'
            };
            
            for (const [name, selector] of Object.entries(elements)) {
                const element = await this.page.$(selector);
                if (!element) {
                    this.logIssue('交易终端', `缺少${name}`, 'high');
                }
            }
            
            // 测试下单功能
            const symbolInput = await this.page.$('input[name="symbol"], input[placeholder*="股票代码"]');
            const priceInput = await this.page.$('input[name="price"], input[placeholder*="价格"]');
            const quantityInput = await this.page.$('input[name="quantity"], input[placeholder*="数量"]');
            const buyBtn = await this.page.$('.buy-btn, button:has-text("买入")');
            
            if (symbolInput && priceInput && quantityInput && buyBtn) {
                await symbolInput.type('000001');
                await priceInput.type('10.5');
                await quantityInput.type('100');
                
                await this.takeScreenshot('order_form_filled');
                
                // 点击买入
                await buyBtn.click();
                await this.page.waitForTimeout(2000);
                
                // 检查订单是否成功
                const successMsg = await this.page.$('.success-message, .el-message--success');
                const errorMsg = await this.page.$('.error-message, .el-message--error');
                
                if (errorMsg) {
                    const text = await errorMsg.textContent();
                    this.logIssue('交易终端', `下单失败: ${text}`, 'high');
                } else if (!successMsg) {
                    this.logIssue('交易终端', '下单后无反馈信息', 'medium');
                }
            }
            
            // 测试快捷键
            await this.page.keyboard.press('F1');
            await this.page.waitForTimeout(500);
            // 检查是否触发买入
            
        } catch (error) {
            this.logIssue('交易终端', `交易终端测试失败: ${error.message}`, 'high');
        }
    }

    async testOrderManagement() {
        console.log('📍 测试订单管理...');
        
        try {
            await this.navigateToSection('订单', 'orders');
            await this.page.waitForTimeout(3000);
            await this.takeScreenshot('order_management');
            
            // 检查订单列表
            const orderRows = await this.page.$$('.order-row, tbody tr');
            if (orderRows.length === 0) {
                this.logIssue('订单管理', '订单列表为空', 'low');
            } else {
                // 测试订单操作
                const firstOrder = orderRows[0];
                const cancelBtn = await firstOrder.$('.cancel-btn, button:has-text("撤单")');
                
                if (cancelBtn) {
                    const isDisabled = await cancelBtn.evaluate(el => el.disabled);
                    if (!isDisabled) {
                        await cancelBtn.click();
                        await this.page.waitForTimeout(1000);
                        
                        // 确认撤单
                        const confirmBtn = await this.page.$('.confirm-btn, button:has-text("确定")');
                        if (confirmBtn) {
                            await confirmBtn.click();
                        }
                    }
                }
            }
            
            // 测试筛选功能
            const filterBtn = await this.page.$('.filter-btn, button:has-text("筛选")');
            if (filterBtn) {
                await filterBtn.click();
                await this.page.waitForTimeout(1000);
                
                // 选择状态筛选
                const statusSelect = await this.page.$('select[name="status"], .status-filter');
                if (statusSelect) {
                    await statusSelect.select('已成交');
                }
            }
            
            // 测试导出功能
            const exportBtn = await this.page.$('.export-btn, button:has-text("导出")');
            if (exportBtn) {
                await exportBtn.click();
                await this.page.waitForTimeout(1000);
                
                // 检查是否有导出选项
                const exportOptions = await this.page.$$('.export-option');
                if (exportOptions.length === 0) {
                    this.logIssue('订单管理', '导出功能无选项', 'low');
                }
            }
            
        } catch (error) {
            this.logIssue('订单管理', `订单管理测试失败: ${error.message}`, 'medium');
        }
    }

    async testStrategyDevelopment() {
        console.log('📍 测试策略开发...');
        
        try {
            await this.navigateToSection('策略', 'strategy');
            await this.page.waitForTimeout(3000);
            await this.takeScreenshot('strategy_development');
            
            // 点击创建策略
            const createBtn = await this.page.$('.create-strategy-btn, button:has-text("创建策略")');
            if (createBtn) {
                await createBtn.click();
                await this.page.waitForTimeout(2000);
                
                // 填写策略信息
                const nameInput = await this.page.$('input[name="strategyName"], input[placeholder*="策略名称"]');
                if (nameInput) {
                    await nameInput.type('测试均线策略');
                }
                
                // 检查代码编辑器
                const codeEditor = await this.page.$('.code-editor, .monaco-editor, textarea[name="code"]');
                if (!codeEditor) {
                    this.logIssue('策略开发', '代码编辑器未加载', 'high');
                } else {
                    // 尝试输入代码
                    await codeEditor.click();
                    await this.page.keyboard.type('# 双均线策略\n');
                }
                
                await this.takeScreenshot('strategy_editor');
                
                // 测试策略模板
                const templateBtn = await this.page.$('.template-btn, button:has-text("选择模板")');
                if (templateBtn) {
                    await templateBtn.click();
                    await this.page.waitForTimeout(1000);
                    
                    const templates = await this.page.$$('.template-item');
                    if (templates.length === 0) {
                        this.logIssue('策略开发', '策略模板列表为空', 'medium');
                    }
                }
                
                // 测试回测功能
                const backtestBtn = await this.page.$('.backtest-btn, button:has-text("回测")');
                if (backtestBtn) {
                    await backtestBtn.click();
                    await this.page.waitForTimeout(2000);
                    
                    // 设置回测参数
                    const startDateInput = await this.page.$('input[name="startDate"]');
                    const endDateInput = await this.page.$('input[name="endDate"]');
                    
                    if (startDateInput && endDateInput) {
                        await startDateInput.type('2024-01-01');
                        await endDateInput.type('2024-12-31');
                    }
                    
                    // 运行回测
                    const runBtn = await this.page.$('.run-backtest-btn, button:has-text("运行回测")');
                    if (runBtn) {
                        await runBtn.click();
                        await this.page.waitForTimeout(5000);
                        
                        // 检查回测结果
                        const resultChart = await this.page.$('.backtest-chart, .result-chart');
                        if (!resultChart) {
                            this.logIssue('策略开发', '回测结果图表未显示', 'high');
                        }
                    }
                }
            } else {
                this.logIssue('策略开发', '找不到创建策略按钮', 'high');
            }
            
        } catch (error) {
            this.logIssue('策略开发', `策略开发测试失败: ${error.message}`, 'high');
        }
    }

    async testPortfolio() {
        console.log('📍 测试投资组合...');
        
        try {
            await this.navigateToSection('持仓', 'portfolio');
            await this.page.waitForTimeout(3000);
            await this.takeScreenshot('portfolio');
            
            // 检查账户总览
            const accountInfo = await this.page.$('.account-summary, .account-info');
            if (!accountInfo) {
                this.logIssue('投资组合', '账户信息未显示', 'high');
            }
            
            // 检查持仓列表
            const positions = await this.page.$$('.position-item, .position-row');
            if (positions.length === 0) {
                this.logIssue('投资组合', '持仓列表为空', 'low');
            }
            
            // 检查收益图表
            const profitChart = await this.page.$('.profit-chart, .returns-chart');
            if (!profitChart) {
                this.logIssue('投资组合', '收益图表未加载', 'medium');
            }
            
            // 测试时间范围切换
            const timeRangeButtons = await this.page.$$('.time-range-btn, .period-selector button');
            for (const btn of timeRangeButtons) {
                await btn.click();
                await this.page.waitForTimeout(1000);
            }
            
        } catch (error) {
            this.logIssue('投资组合', `投资组合测试失败: ${error.message}`, 'medium');
        }
    }

    async testRiskManagement() {
        console.log('📍 测试风险管理...');
        
        try {
            await this.navigateToSection('风控', 'risk');
            await this.page.waitForTimeout(3000);
            await this.takeScreenshot('risk_management');
            
            // 检查风险指标
            const riskMetrics = await this.page.$$('.risk-metric, .risk-indicator');
            if (riskMetrics.length === 0) {
                this.logIssue('风险管理', '风险指标未显示', 'high');
            }
            
            // 检查风险限额设置
            const limitSettings = await this.page.$('.risk-limits, .limit-settings');
            if (!limitSettings) {
                this.logIssue('风险管理', '风险限额设置未找到', 'medium');
            }
            
            // 测试风险告警
            const alertList = await this.page.$('.risk-alerts, .alert-list');
            if (!alertList) {
                this.logIssue('风险管理', '风险告警列表未显示', 'medium');
            }
            
        } catch (error) {
            this.logIssue('风险管理', `风险管理测试失败: ${error.message}`, 'medium');
        }
    }

    async testSettings() {
        console.log('📍 测试系统设置...');
        
        try {
            await this.navigateToSection('设置', 'settings');
            await this.page.waitForTimeout(3000);
            await this.takeScreenshot('settings');
            
            // 测试各个设置项
            const settingTabs = await this.page.$$('.setting-tab, .tab-item');
            for (const tab of settingTabs) {
                await tab.click();
                await this.page.waitForTimeout(1000);
            }
            
            // 测试主题切换
            const themeSwitch = await this.page.$('.theme-switch, input[name="theme"]');
            if (themeSwitch) {
                await themeSwitch.click();
                await this.page.waitForTimeout(1000);
                
                // 检查主题是否切换
                const isDark = await this.page.evaluate(() => {
                    return document.body.classList.contains('dark') || 
                           document.documentElement.classList.contains('dark');
                });
                
                if (!isDark) {
                    this.logIssue('设置', '主题切换无效', 'low');
                }
            }
            
            // 测试保存设置
            const saveBtn = await this.page.$('.save-settings-btn, button:has-text("保存")');
            if (saveBtn) {
                await saveBtn.click();
                await this.page.waitForTimeout(1000);
            }
            
        } catch (error) {
            this.logIssue('设置', `设置测试失败: ${error.message}`, 'low');
        }
    }

    async navigateToSection(text, urlPart) {
        try {
            // 尝试通过文本找到导航链接
            const navLink = await this.page.$(`a:has-text("${text}"), .nav-item:has-text("${text}")`);
            
            if (navLink) {
                await navLink.click();
                await this.page.waitForTimeout(2000);
            } else {
                // 直接导航到URL
                await this.page.goto(`${this.baseUrl}/${urlPart}`, { waitUntil: 'networkidle0' });
            }
        } catch (error) {
            this.logIssue('导航', `无法导航到${text}页面: ${error.message}`, 'medium');
        }
    }

    async generateReport() {
        console.log('\n📊 生成测试报告...\n');
        
        const report = {
            testTime: new Date().toISOString(),
            totalIssues: this.testResults.length,
            issuesByCategory: {},
            issuesBySeverity: {
                critical: 0,
                high: 0,
                medium: 0,
                low: 0
            },
            screenshots: this.screenshots,
            recommendations: []
        };
        
        // 分析问题
        for (const issue of this.testResults) {
            // 按类别统计
            if (!report.issuesByCategory[issue.category]) {
                report.issuesByCategory[issue.category] = [];
            }
            report.issuesByCategory[issue.category].push(issue);
            
            // 按严重程度统计
            report.issuesBySeverity[issue.severity]++;
        }
        
        // 生成建议
        if (report.issuesBySeverity.critical > 0) {
            report.recommendations.push('立即修复关键问题，这些问题会导致用户无法使用核心功能');
        }
        
        if (report.issuesBySeverity.high > 2) {
            report.recommendations.push('优先处理高严重性问题，改善用户体验');
        }
        
        if (report.issuesByCategory['市场数据']?.length > 0) {
            report.recommendations.push('加强实时数据更新机制，确保数据及时性');
        }
        
        if (report.issuesByCategory['交易终端']?.length > 0) {
            report.recommendations.push('优化交易界面，添加更多交易确认和风险提示');
        }
        
        // 输出报告
        console.log('=== 用户体验测试报告 ===\n');
        console.log(`总问题数: ${report.totalIssues}`);
        console.log('\n问题严重性分布:');
        console.log(`  🔴 关键: ${report.issuesBySeverity.critical}`);
        console.log(`  🟠 高: ${report.issuesBySeverity.high}`);
        console.log(`  🟡 中: ${report.issuesBySeverity.medium}`);
        console.log(`  🟢 低: ${report.issuesBySeverity.low}`);
        
        console.log('\n问题分类:');
        for (const [category, issues] of Object.entries(report.issuesByCategory)) {
            console.log(`  ${category}: ${issues.length} 个问题`);
        }
        
        console.log('\n改进建议:');
        report.recommendations.forEach((rec, index) => {
            console.log(`  ${index + 1}. ${rec}`);
        });
        
        // 保存详细报告
        await fs.writeFile(
            'user_experience_report.json',
            JSON.stringify(report, null, 2),
            'utf-8'
        );
        
        console.log('\n详细报告已保存到: user_experience_report.json');
        console.log(`截图保存在: screenshots/ 目录`);
        
        return report;
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    async run() {
        try {
            await this.init();
            await this.testUserJourney();
            await this.generateReport();
        } catch (error) {
            console.error('测试过程中发生错误:', error);
        } finally {
            await this.cleanup();
        }
    }
}

// 创建测试实例并运行
const tester = new UserExperienceTest();
tester.run().catch(console.error);