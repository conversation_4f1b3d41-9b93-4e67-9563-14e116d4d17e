{"timestamp": "2025-07-27T18:59:26.445758", "backend_analysis": {"total_apis": 9, "successful_apis": 9, "success_rate": 100.0, "details": {"健康检查": {"status": 200, "response_time": 0.007300853729248047, "success": true, "data_preview": "{'status': 'healthy'}"}, "登录API": {"status": 200, "response_time": 0.0017240047454833984, "success": true, "data_preview": "{'user': {'id': 'admin_user', 'username': 'admin', 'email': '<EMAIL>'}, 'token': 'token_1bed3611c595e8ea0be9edd5d480ad83'}"}, "注册API": {"status": 200, "response_time": 0.0018322467803955078, "success": true, "data_preview": "{'user': {'id': 2, 'username': 'test', 'email': '<EMAIL>'}, 'token': 'token_94c248f74bfce1bd8d2f2df0f8a85d87'}"}, "市场概览API": {"status": 200, "response_time": 0.0013289451599121094, "success": true, "data_preview": "{'success': True, 'data': {'market_status': 'open', 'timestamp': '2025-07-27T18:59:26.457904', 'indices': [{'name': '上证指数', 'symbol': '000001.SH', 'current': 3245.68, 'change': 15.23, 'change_percent'"}, "交易账户API": {"status": 200, "response_time": 0.0017228126525878906, "success": true, "data_preview": "{'success': True, 'data': [{'account_id': 'MAIN_001', 'account_name': '主账户', 'account_type': '股票账户', 'status': 'active', 'balance': 1000000.0, 'available': 850000.0, 'frozen': 150000.0, 'market_value'"}, "板块数据API": {"status": 200, "response_time": 0.0009429454803466797, "success": true, "data_preview": "{'success': True, 'data': [{'name': '科技', 'change_percent': 2.45, 'stocks_count': 156, 'market_cap': *************}, {'name': '金融', 'change_percent': 1.23, 'stocks_count': 89, 'market_cap': **********"}, "排行榜API": {"status": 200, "response_time": 0.0009348392486572266, "success": true, "data_preview": "{'success': True, 'data': [{'symbol': '000001', 'name': '平安银行', 'change_percent': 9.98, 'price': 13.75}, {'symbol': '600519', 'name': '贵州茅台', 'change_percent': 8.45, 'price': 1820.5}, {'symbol': '0008"}, "自选股API": {"status": 200, "response_time": 0.0008950233459472656, "success": true, "data_preview": "{'success': True, 'data': [{'symbol': '000001', 'name': '平安银行', 'price': 12.5, 'change_percent': 1.22}, {'symbol': '600519', 'name': '贵州茅台', 'price': 1680.5, 'change_percent': 0.91}, {'symbol': '00085"}, "市场新闻API": {"status": 200, "response_time": 0.0011739730834960938, "success": true, "data_preview": "{'success': True, 'data': [{'id': 1, 'title': 'A股三大指数集体上涨，科技股表现强势', 'summary': '今日A股市场表现活跃，上证指数上涨0.47%...', 'source': '财经新闻', 'publish_time': '2025-07-27T18:59:26.465381', 'url': '#'}, {'id': 2, 'titl"}}}, "frontend_analysis": {"total_pages": 8, "successful_pages": 8, "success_rate": 100.0, "details": {"主页": {"accessible": true, "load_time": 1.9159350395202637, "title": "量化交易平台", "has_content": true, "has_navigation": true, "has_main_content": true, "has_buttons": true, "success": true}, "登录页面": {"accessible": true, "load_time": 1.0522749423980713, "title": "量化交易平台", "has_content": true, "has_navigation": false, "has_main_content": false, "has_buttons": true, "success": true}, "行情页面": {"accessible": true, "load_time": 1.6140508651733398, "title": "量化交易平台", "has_content": true, "has_navigation": true, "has_main_content": true, "has_buttons": true, "success": true}, "交易页面": {"accessible": true, "load_time": 1.2857987880706787, "title": "量化交易平台", "has_content": true, "has_navigation": true, "has_main_content": true, "has_buttons": true, "success": true}, "策略页面": {"accessible": true, "load_time": 1.2832162380218506, "title": "量化交易平台", "has_content": true, "has_navigation": true, "has_main_content": true, "has_buttons": true, "success": true}, "回测页面": {"accessible": true, "load_time": 1.2189648151397705, "title": "量化交易平台", "has_content": true, "has_navigation": true, "has_main_content": true, "has_buttons": true, "success": true}, "风控页面": {"accessible": true, "load_time": 1.5583999156951904, "title": "量化交易平台", "has_content": true, "has_navigation": true, "has_main_content": true, "has_buttons": true, "success": true}, "投资组合页面": {"accessible": true, "load_time": 1.088007926940918, "title": "量化交易平台", "has_content": true, "has_navigation": true, "has_main_content": true, "has_buttons": true, "success": true}}}, "user_experience": {"flows": {"登录流程": true, "拼图验证": false, "页面导航": true, "完整认证": false}, "success_count": 2, "total_flows": 4, "success_rate": 50.0}, "overall_assessment": {"overall_score": 90.0, "backend_score": 100.0, "frontend_score": 100.0, "ux_score": 50.0, "grade": "🏆 卓越 (A+)"}}