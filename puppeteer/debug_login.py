#!/usr/bin/env python3
"""
调试登录问题
"""

import asyncio
import logging
from playwright.async_api import async_playwright

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def debug_login():
    playwright = await async_playwright().start()
    browser = await playwright.chromium.launch(headless=False)
    page = await browser.new_page()
    
    try:
        # 访问登录页面
        logger.info("访问登录页面...")
        await page.goto("http://localhost:5173/login")
        await page.wait_for_load_state('networkidle')
        
        # 清除存储
        await page.evaluate("() => { localStorage.clear(); sessionStorage.clear(); }")
        await page.reload()
        await page.wait_for_load_state('networkidle')
        
        # 截图
        await page.screenshot(path="login_page.png")
        logger.info("登录页面截图已保存: login_page.png")
        
        # 查找演示登录按钮
        demo_buttons = await page.query_selector_all('button')
        logger.info(f"找到 {len(demo_buttons)} 个按钮")
        
        for i, button in enumerate(demo_buttons):
            text = await button.inner_text()
            logger.info(f"按钮 {i+1}: '{text.strip()}'")
            
            if '演示登录' in text or 'demo' in text.lower():
                logger.info(f"找到演示登录按钮: '{text.strip()}'")
                
                # 点击按钮
                await button.click()
                logger.info("点击演示登录按钮")
                
                # 等待跳转
                await asyncio.sleep(5)
                
                current_url = page.url
                logger.info(f"当前URL: {current_url}")
                
                # 截图
                await page.screenshot(path="after_demo_login.png")
                logger.info("登录后截图已保存: after_demo_login.png")
                
                break
        else:
            logger.error("未找到演示登录按钮")
            
    except Exception as e:
        logger.error(f"调试过程中发生错误: {e}")
    finally:
        await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_login())
