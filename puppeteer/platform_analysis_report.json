{"timestamp": "2025-07-27T15:56:24.757788", "frontend_tests": {"主页": {"accessible": true, "load_time": 1.5780420303344727, "title": "量化交易平台", "has_content": true, "url": "http://localhost:5173/"}, "登录页面": {"accessible": true, "load_time": 1.282984972000122, "title": "量化交易平台", "has_content": true, "url": "http://localhost:5173/login"}, "行情页面": {"accessible": true, "load_time": 1.1968660354614258, "title": "量化交易平台", "has_content": true, "url": "http://localhost:5173/market"}, "交易页面": {"accessible": true, "load_time": 1.3588078022003174, "title": "量化交易平台", "has_content": true, "url": "http://localhost:5173/trading"}, "策略页面": {"accessible": true, "load_time": 1.377561092376709, "title": "量化交易平台", "has_content": true, "url": "http://localhost:5173/strategy"}, "回测页面": {"accessible": true, "load_time": 1.195802927017212, "title": "量化交易平台", "has_content": true, "url": "http://localhost:5173/backtest"}, "风控页面": {"accessible": true, "load_time": 1.0412349700927734, "title": "量化交易平台", "has_content": true, "url": "http://localhost:5173/risk"}, "投资组合页面": {"accessible": true, "load_time": 1.0734431743621826, "title": "量化交易平台", "has_content": true, "url": "http://localhost:5173/portfolio"}}, "backend_tests": {"健康检查": {"status": 200, "response_time": 26.776150941848755, "success": true, "data_preview": "{'status': 'healthy'}"}, "登录API": {"status": 200, "response_time": 0.0027320384979248047, "success": true, "data_preview": "{'user': {'id': 'admin_user', 'username': 'admin', 'email': '<EMAIL>'}, 'token': 'token_cd2701f2030b5dce6565609320ab2de2'}"}, "注册API": {"status": 200, "response_time": 0.0024690628051757812, "success": true, "data_preview": "{'user': {'id': 2, 'username': 'test', 'email': '<EMAIL>'}, 'token': 'token_793cac6d3de599bd07e925dfb2ff2703'}"}, "市场概览API": {"status": 200, "response_time": 0.0018470287322998047, "success": true, "data_preview": "{'success': True, 'data': {'market_status': 'open', 'timestamp': '2025-07-27T15:56:51.541184', 'indices': [{'name': '上证指数', 'symbol': '000001.SH', 'current': 3245.68, 'change': 15.23, 'change_percent'"}, "交易账户API": {"status": 200, "response_time": 0.0013170242309570312, "success": true, "data_preview": "{'success': True, 'data': [{'account_id': 'MAIN_001', 'account_name': '主账户', 'account_type': '股票账户', 'status': 'active', 'balance': 1000000.0, 'available': 850000.0, 'frozen': 150000.0, 'market_value'"}, "板块数据API": {"status": 200, "response_time": 0.001216888427734375, "success": true, "data_preview": "{'success': True, 'data': [{'name': '科技', 'change_percent': 2.45, 'stocks_count': 156, 'market_cap': *************}, {'name': '金融', 'change_percent': 1.23, 'stocks_count': 89, 'market_cap': **********"}, "排行榜API": {"status": 200, "response_time": 0.0012540817260742188, "success": true, "data_preview": "{'success': True, 'data': [{'symbol': '000001', 'name': '平安银行', 'change_percent': 9.98, 'price': 13.75}, {'symbol': '600519', 'name': '贵州茅台', 'change_percent': 8.45, 'price': 1820.5}, {'symbol': '0008"}, "自选股API": {"status": 200, "response_time": 0.0013988018035888672, "success": true, "data_preview": "{'success': True, 'data': [{'symbol': '000001', 'name': '平安银行', 'price': 12.5, 'change_percent': 1.22}, {'symbol': '600519', 'name': '贵州茅台', 'price': 1680.5, 'change_percent': 0.91}, {'symbol': '00085"}, "市场新闻API": {"status": 200, "response_time": 0.001928091049194336, "success": true, "data_preview": "{'success': True, 'data': [{'id': 1, 'title': 'A股三大指数集体上涨，科技股表现强势', 'summary': '今日A股市场表现活跃，上证指数上涨0.47%...', 'source': '财经新闻', 'publish_time': '2025-07-27T15:56:51.551635', 'url': '#'}, {'id': 2, 'titl"}}, "integration_tests": {"authentication_flow": {"login_page_access": true, "demo_login_available": true, "puzzle_verification": false, "main_app_access": false, "overall_flow": false}}, "overall_score": 82.0, "issues": [], "recommendations": []}