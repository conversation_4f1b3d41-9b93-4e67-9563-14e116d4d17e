{"test_timestamp": "2025-08-01T14:14:44.830458", "total_score": 20, "score_breakdown": {"platform_access": 0, "navigation": 0, "authentication": 0, "backend_connectivity": 20, "ui_functionality": 0}, "detailed_results": {"test_overview": {"error": "Page.goto: Timeout 10000ms exceeded.\nCall log:\n  - navigating to \"http://localhost:5175/\", waiting until \"load\"\n", "accessible": false}, "authentication_test": {}, "navigation_test": {}, "backend_connectivity": {"health_check": {"status": "accessible", "status_code": 200, "response_time": "< 5s"}, "api_docs": {"status": "accessible", "status_code": 200, "response_time": "< 5s"}, "register_api": {"status": "accessible", "status_code": 404, "response_time": "< 5s"}, "login_api": {"status": "accessible", "status_code": 405, "response_time": "< 5s"}, "market_data_api": {"status": "accessible", "status_code": 200, "response_time": "< 5s"}, "trading_api": {"status": "accessible", "status_code": 404, "response_time": "< 5s"}}, "ui_functionality": {}, "issues_found": [{"type": "request_failed", "url": "http://localhost:5175/src/components/common/AppButton/index.vue?vue&type=style&index=0&scoped=ed89779c&lang.css", "method": "GET", "timestamp": "2025-08-01T14:14:41.471023"}, {"type": "request_failed", "url": "http://localhost:5175/src/assets/styles/element.scss", "method": "GET", "timestamp": "2025-08-01T14:14:41.471652"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/chunk-VNW6TM4N.js?v=971c4365", "method": "GET", "timestamp": "2025-08-01T14:14:41.472252"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/chunk-A6HD4DJF.js?v=971c4365", "method": "GET", "timestamp": "2025-08-01T14:14:41.472643"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.pnpm/element-plus@2.10.4_vue@3.5.17_typescript@5.8.3_/node_modules/element-plus/theme-chalk/src/card.scss", "method": "GET", "timestamp": "2025-08-01T14:14:41.473060"}, {"type": "request_failed", "url": "http://localhost:5175/src/composables/trading/useOrderForm.ts", "method": "GET", "timestamp": "2025-08-01T14:14:41.473458"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/element-plus_es_components_descriptions-item_style_index.js?v=1c14e434", "method": "GET", "timestamp": "2025-08-01T14:14:41.473838"}, {"type": "request_failed", "url": "http://localhost:5175/src/components/strategy/StrategyCard/index.vue?vue&type=style&index=0&scoped=e608fdc3&lang.css", "method": "GET", "timestamp": "2025-08-01T14:14:41.476092"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/element-plus_es_components_radio_style_index.js?v=a60dc95b", "method": "GET", "timestamp": "2025-08-01T14:14:41.479520"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/chunk-OHVJNOQA.js?v=971c4365", "method": "GET", "timestamp": "2025-08-01T14:14:41.480483"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/element-plus_es_components_dropdown_style_index.js?v=6a5f67ac", "method": "GET", "timestamp": "2025-08-01T14:14:41.484170"}, {"type": "request_failed", "url": "http://localhost:5175/src/services/auth.service.ts", "method": "GET", "timestamp": "2025-08-01T14:14:41.484744"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/element-plus_es_components_radio-button_style_index.js?v=ad224651", "method": "GET", "timestamp": "2025-08-01T14:14:41.485245"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.pnpm/element-plus@2.10.4_vue@3.5.17_typescript@5.8.3_/node_modules/element-plus/theme-chalk/src/tooltip.scss", "method": "GET", "timestamp": "2025-08-01T14:14:41.485765"}, {"type": "request_failed", "url": "http://localhost:5175/src/composables/core/useFullscreen.ts", "method": "GET", "timestamp": "2025-08-01T14:14:41.486495"}, {"type": "request_failed", "url": "http://localhost:5175/src/components/common/AppModal/index.vue?vue&type=style&index=0&scoped=a3674a00&lang.css", "method": "GET", "timestamp": "2025-08-01T14:14:41.487291"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/lodash-es.js?v=0f610ba2", "method": "GET", "timestamp": "2025-08-01T14:14:41.487820"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/chunk-UKHBKVFE.js?v=971c4365", "method": "GET", "timestamp": "2025-08-01T14:14:41.488144"}, {"type": "request_failed", "url": "http://localhost:5175/src/composables/chart/useKLineData.ts", "method": "GET", "timestamp": "2025-08-01T14:14:41.488695"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.pnpm/element-plus@2.10.4_vue@3.5.17_typescript@5.8.3_/node_modules/element-plus/theme-chalk/src/select.scss", "method": "GET", "timestamp": "2025-08-01T14:14:41.489018"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.pnpm/vue-demi@0.14.10_vue@3.5.17_typescript@5.8.3_/node_modules/vue-demi/lib/index.mjs?v=0f610ba2", "method": "GET", "timestamp": "2025-08-01T14:14:41.489296"}, {"type": "request_failed", "url": "http://localhost:5175/src/composables/chart/useIndicators.ts", "method": "GET", "timestamp": "2025-08-01T14:14:41.489603"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/chunk-W7N6MO67.js?v=971c4365", "method": "GET", "timestamp": "2025-08-01T14:14:41.489865"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/element-plus_es_components_descriptions_style_index.js?v=3fb47524", "method": "GET", "timestamp": "2025-08-01T14:14:41.490139"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/element-plus_es_components_tag_style_index.js?v=7adec3c3", "method": "GET", "timestamp": "2025-08-01T14:14:41.490351"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/chunk-OTAZ3Z4A.js?v=971c4365", "method": "GET", "timestamp": "2025-08-01T14:14:41.490655"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.pnpm/element-plus@2.10.4_vue@3.5.17_typescript@5.8.3_/node_modules/element-plus/theme-chalk/src/option-group.scss", "method": "GET", "timestamp": "2025-08-01T14:14:41.490932"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.pnpm/@vueuse+shared@9.13.0_vue@3.5.17_typescript@5.8.3_/node_modules/@vueuse/shared/index.mjs?v=0f610ba2", "method": "GET", "timestamp": "2025-08-01T14:14:41.491177"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/chunk-FI4NSCJJ.js?v=971c4365", "method": "GET", "timestamp": "2025-08-01T14:14:41.491390"}, {"type": "request_failed", "url": "http://localhost:5175/src/components/market/StockCard.vue?vue&type=style&index=0&scoped=31a0a282&lang.css", "method": "GET", "timestamp": "2025-08-01T14:14:41.491586"}, {"type": "request_failed", "url": "http://localhost:5175/src/composables/useMarketData.ts", "method": "GET", "timestamp": "2025-08-01T14:14:41.491812"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/element-plus_es_components_dialog_style_index.js?v=3678a7d9", "method": "GET", "timestamp": "2025-08-01T14:14:41.492040"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.pnpm/element-plus@2.10.4_vue@3.5.17_typescript@5.8.3_/node_modules/element-plus/theme-chalk/src/table-column.scss", "method": "GET", "timestamp": "2025-08-01T14:14:41.492379"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/element-plus_es_components_input_style_index.js?v=479623c7", "method": "GET", "timestamp": "2025-08-01T14:14:41.492639"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/element-plus_es_components_alert_style_index.js?v=d5b831c5", "method": "GET", "timestamp": "2025-08-01T14:14:41.492850"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/element-plus_es_components_radio-group_style_index.js?v=4ceed1d2", "method": "GET", "timestamp": "2025-08-01T14:14:41.493178"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/element-plus_es_components_date-picker_style_index.js?v=a409cf13", "method": "GET", "timestamp": "2025-08-01T14:14:41.493395"}, {"type": "request_failed", "url": "http://localhost:5175/src/components/trading/OrderForm/index.vue?vue&type=style&index=0&scoped=f078bb0c&lang.css", "method": "GET", "timestamp": "2025-08-01T14:14:41.494838"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/numeral.js?v=0f610ba2", "method": "GET", "timestamp": "2025-08-01T14:14:41.495647"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/dayjs.js?v=0f610ba2", "method": "GET", "timestamp": "2025-08-01T14:14:41.496153"}, {"type": "request_failed", "url": "http://localhost:5175/src/components/charts/KLineChart/index.vue?vue&type=style&index=0&scoped=74dd5bca&lang.css", "method": "GET", "timestamp": "2025-08-01T14:14:41.496421"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/chunk-BJ7YIWEB.js?v=971c4365", "method": "GET", "timestamp": "2025-08-01T14:14:41.496665"}, {"type": "request_failed", "url": "http://localhost:5175/src/components/common/AppCard/index.vue?vue&type=style&index=0&scoped=c06f3848&lang.css", "method": "GET", "timestamp": "2025-08-01T14:14:41.496934"}, {"type": "request_failed", "url": "http://localhost:5175/src/components/common/SliderCaptcha/index.vue?vue&type=style&index=0&scoped=52125763&lang.css", "method": "GET", "timestamp": "2025-08-01T14:14:41.497202"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.pnpm/element-plus@2.10.4_vue@3.5.17_typescript@5.8.3_/node_modules/element-plus/theme-chalk/src/table.scss", "method": "GET", "timestamp": "2025-08-01T14:14:41.497565"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/element-plus_es_components_dropdown-menu_style_index.js?v=ed5b416b", "method": "GET", "timestamp": "2025-08-01T14:14:41.497846"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/element-plus_es_components_form-item_style_index.js?v=a1dc5654", "method": "GET", "timestamp": "2025-08-01T14:14:41.498059"}, {"type": "request_failed", "url": "http://localhost:5175/src/stores/modules/trading.ts", "method": "GET", "timestamp": "2025-08-01T14:14:41.498251"}, {"type": "request_failed", "url": "http://localhost:5175/src/components/common/VirtualTable/index.vue?vue&type=style&index=0&scoped=db1486b9&lang.css", "method": "GET", "timestamp": "2025-08-01T14:14:41.498665"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/chunk-HETSV2L5.js?v=971c4365", "method": "GET", "timestamp": "2025-08-01T14:14:41.498875"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/element-plus_es_components_empty_style_index.js?v=41cd1cb2", "method": "GET", "timestamp": "2025-08-01T14:14:41.499072"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/chunk-DC5AMYBS.js?v=971c4365", "method": "GET", "timestamp": "2025-08-01T14:14:41.499277"}, {"type": "request_failed", "url": "http://localhost:5175/src/composables/chart/useChart.ts", "method": "GET", "timestamp": "2025-08-01T14:14:41.499505"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/element-plus_es_components_dropdown-item_style_index.js?v=3db4c29b", "method": "GET", "timestamp": "2025-08-01T14:14:41.499812"}, {"type": "request_failed", "url": "http://localhost:5175/src/utils/format/financial.ts", "method": "GET", "timestamp": "2025-08-01T14:14:41.500164"}, {"type": "request_failed", "url": "http://localhost:5175/src/components/charts/DepthChart/index.vue?vue&type=style&index=0&scoped=e2a713c2&lang.scss", "method": "GET", "timestamp": "2025-08-01T14:14:41.500398"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/element-plus_es_components_autocomplete_style_index.js?v=154c4ebc", "method": "GET", "timestamp": "2025-08-01T14:14:41.500603"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/element-plus_es_components_form_style_index.js?v=f5c70454", "method": "GET", "timestamp": "2025-08-01T14:14:41.500850"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/chunk-LDJLCIMQ.js?v=971c4365", "method": "GET", "timestamp": "2025-08-01T14:14:41.501248"}, {"type": "request_failed", "url": "http://localhost:5175/src/components/backtest/BacktestForm.vue?vue&type=style&index=0&scoped=54368c44&lang.css", "method": "GET", "timestamp": "2025-08-01T14:14:41.501690"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/element-plus_es_components_button-group_style_index.js?v=964c34e6", "method": "GET", "timestamp": "2025-08-01T14:14:41.501995"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/chunk-GUB7NKQY.js?v=971c4365", "method": "GET", "timestamp": "2025-08-01T14:14:41.502212"}, {"type": "request_failed", "url": "http://localhost:5175/node_modules/.vite/deps/element-plus_es_components_input-number_style_index.js?v=699e3171", "method": "GET", "timestamp": "2025-08-01T14:14:41.502418"}, {"type": "request_failed", "url": "http://localhost:5175/src/composables/useWebSocket.ts", "method": "GET", "timestamp": "2025-08-01T14:14:41.502620"}], "recommendations": ["修复平台基础访问问题", "优化页面路由和导航功能", "完善用户认证和登录流程", "增强UI交互功能", "修复前端JavaScript错误和网络请求问题"]}, "recommendations": ["修复平台基础访问问题", "优化页面路由和导航功能", "完善用户认证和登录流程", "增强UI交互功能", "修复前端JavaScript错误和网络请求问题"]}