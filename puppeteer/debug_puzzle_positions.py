#!/usr/bin/env python3
"""
调试拼图位置和滑动位置
"""

import asyncio
import logging
from playwright.async_api import async_playwright

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def debug_puzzle_positions():
    playwright = await async_playwright().start()
    browser = await playwright.chromium.launch(headless=False)
    page = await browser.new_page()
    
    try:
        # 登录并跳转到拼图验证页面
        await page.goto("http://localhost:5173/login")
        await page.wait_for_load_state('networkidle')
        
        await page.evaluate("() => { localStorage.clear(); sessionStorage.clear(); }")
        await page.reload()
        await page.wait_for_load_state('networkidle')
        
        demo_btn = await page.query_selector('button:has-text("演示登录")')
        if demo_btn:
            await demo_btn.click()
            await asyncio.sleep(3)
        
        # 等待页面加载
        await asyncio.sleep(2)
        
        # 获取拼图相关的详细信息
        puzzle_debug_info = await page.evaluate("""
            () => {
                // 尝试获取Vue组件实例
                const app = document.querySelector('#app');
                let vueData = null;
                
                if (app && app.__vue__) {
                    const vm = app.__vue__;
                    // 尝试获取组件数据
                    if (vm.$data) {
                        vueData = {
                            blockLeft: vm.$data.blockLeft,
                            sliderLeft: vm.$data.sliderLeft,
                            puzzleData: vm.$data.puzzleData,
                            isSuccess: vm.$data.isSuccess
                        };
                    }
                }
                
                // 获取DOM元素信息
                const sliderBtn = document.querySelector('.slider-btn');
                const sliderTrack = document.querySelector('.slider-track');
                const puzzleCanvas = document.querySelector('.puzzle-canvas');
                const blockCanvas = document.querySelector('.puzzle-block');
                
                return {
                    vueData: vueData,
                    elements: {
                        sliderBtn: sliderBtn ? {
                            rect: sliderBtn.getBoundingClientRect(),
                            style: sliderBtn.style.left
                        } : null,
                        sliderTrack: sliderTrack ? {
                            rect: sliderTrack.getBoundingClientRect()
                        } : null,
                        puzzleCanvas: puzzleCanvas ? {
                            rect: puzzleCanvas.getBoundingClientRect(),
                            width: puzzleCanvas.width,
                            height: puzzleCanvas.height
                        } : null,
                        blockCanvas: blockCanvas ? {
                            rect: blockCanvas.getBoundingClientRect(),
                            style: blockCanvas.style.left,
                            width: blockCanvas.width,
                            height: blockCanvas.height
                        } : null
                    }
                };
            }
        """)
        
        logger.info("🔍 拼图调试信息:")
        logger.info(f"Vue数据: {puzzle_debug_info['vueData']}")
        logger.info("DOM元素信息:")
        for element_name, element_info in puzzle_debug_info['elements'].items():
            if element_info:
                logger.info(f"  {element_name}: {element_info}")
            else:
                logger.info(f"  {element_name}: 未找到")
        
        # 尝试手动滑动到特定位置
        slider_btn = await page.query_selector('.slider-btn')
        if slider_btn:
            btn_box = await slider_btn.bounding_box()
            track = await page.query_selector('.slider-track')
            track_box = await track.bounding_box()
            
            if btn_box and track_box:
                logger.info(f"滑动按钮位置: {btn_box}")
                logger.info(f"滑动轨道位置: {track_box}")
                
                # 计算轨道可用宽度
                available_width = track_box['width'] - btn_box['width']
                logger.info(f"可用滑动宽度: {available_width}px")
                
                # 尝试滑动到不同百分比位置
                for percent in [20, 40, 60, 80]:
                    logger.info(f"\n尝试滑动到 {percent}% 位置...")
                    
                    # 刷新拼图
                    refresh_btn = await page.query_selector('.refresh-btn')
                    if refresh_btn:
                        await refresh_btn.click()
                        await asyncio.sleep(1)
                    
                    # 重新获取位置信息
                    btn_box = await slider_btn.bounding_box()
                    
                    start_x = btn_box['x'] + btn_box['width'] / 2
                    start_y = btn_box['y'] + btn_box['height'] / 2
                    target_x = track_box['x'] + (available_width * percent / 100) + btn_box['width'] / 2
                    
                    logger.info(f"  从 ({start_x:.1f}, {start_y:.1f}) 滑动到 ({target_x:.1f}, {start_y:.1f})")
                    
                    # 执行滑动
                    await page.mouse.move(start_x, start_y)
                    await page.mouse.down()
                    
                    steps = 20
                    for i in range(steps + 1):
                        progress = i / steps
                        current_x = start_x + (target_x - start_x) * progress
                        await page.mouse.move(current_x, start_y)
                        await asyncio.sleep(0.02)
                    
                    await page.mouse.up()
                    await asyncio.sleep(1)
                    
                    # 检查结果
                    result_info = await page.evaluate("""
                        () => {
                            const successBtn = document.querySelector('.slider-btn-success');
                            const successMsg = document.querySelector('.result-message.success');
                            const errorMsg = document.querySelector('.result-message.error');
                            const sliderText = document.querySelector('.slider-text');
                            
                            return {
                                hasSuccessBtn: !!successBtn,
                                hasSuccessMsg: !!successMsg,
                                hasErrorMsg: !!errorMsg,
                                sliderText: sliderText ? sliderText.textContent : null
                            };
                        }
                    """)
                    
                    logger.info(f"  验证结果: {result_info}")
                    
                    if result_info['hasSuccessBtn'] or result_info['hasSuccessMsg']:
                        logger.info(f"✅ 在 {percent}% 位置验证成功！")
                        break
                else:
                    logger.error("❌ 所有位置都验证失败")
        
        # 最终截图
        await page.screenshot(path="puzzle_debug.png")
        logger.info("📸 调试截图已保存: puzzle_debug.png")
        
    except Exception as e:
        logger.error(f"❌ 调试过程中发生错误: {e}")
    finally:
        await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_puzzle_positions())
