#!/usr/bin/env python3
"""
测试缺失的后端API
"""

import asyncio
import aiohttp
import logging
import json

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8000"

async def test_api_endpoint(session, method, endpoint, data=None, expected_status=200):
    """测试API端点"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method.upper() == "GET":
            async with session.get(url) as response:
                status = response.status
                content = await response.text()
        elif method.upper() == "POST":
            headers = {"Content-Type": "application/json"}
            async with session.post(url, json=data, headers=headers) as response:
                status = response.status
                content = await response.text()
        else:
            logger.error(f"不支持的HTTP方法: {method}")
            return False
        
        if status == expected_status:
            logger.info(f"✅ {method} {endpoint} - 状态码: {status}")
            try:
                json_content = json.loads(content)
                if "success" in json_content and json_content["success"]:
                    logger.info(f"   响应成功: {json_content.get('message', 'OK')}")
                elif "data" in json_content:
                    logger.info(f"   返回数据: {len(json_content['data']) if isinstance(json_content['data'], list) else 'object'}")
                else:
                    logger.info(f"   响应内容: {content[:100]}...")
            except json.JSONDecodeError:
                logger.info(f"   响应内容: {content[:100]}...")
            return True
        else:
            logger.error(f"❌ {method} {endpoint} - 状态码: {status} (期望: {expected_status})")
            logger.error(f"   响应内容: {content[:200]}...")
            return False
            
    except Exception as e:
        logger.error(f"❌ {method} {endpoint} - 请求失败: {e}")
        return False

async def test_missing_apis():
    """测试之前缺失的API"""
    logger.info("🚀 开始测试缺失的后端API...")
    
    async with aiohttp.ClientSession() as session:
        test_results = []
        
        # 1. 测试市场概览API
        logger.info("\n�� 测试市场概览API...")
        result = await test_api_endpoint(session, "GET", "/api/v1/market/overview")
        test_results.append(("市场概览API", result))
        
        # 2. 测试交易账户API
        logger.info("\n💰 测试交易账户API...")
        result = await test_api_endpoint(session, "GET", "/api/v1/trading/accounts")
        test_results.append(("交易账户API", result))
        
        # 3. 测试注册API (POST方法)
        logger.info("\n👤 测试注册API...")
        register_data = {
            "username": "testuser123",
            "email": "<EMAIL>",
            "password": "testpass123"
        }
        result = await test_api_endpoint(session, "POST", "/api/v1/auth/register", register_data)
        test_results.append(("注册API", result))
        
        # 统计结果
        logger.info("\n📊 关键API测试结果:")
        success_count = sum(1 for _, result in test_results if result)
        total_count = len(test_results)
        success_rate = (success_count / total_count) * 100
        
        logger.info(f"   成功率: {success_rate:.1f}% ({success_count}/{total_count})")
        
        if success_rate >= 90:
            logger.info("🎉 关键API修复成功！")
        elif success_rate >= 70:
            logger.info("✅ 大部分API正常工作")
        else:
            logger.warning("⚠️ 部分关键API仍需修复")

if __name__ == "__main__":
    asyncio.run(test_missing_apis())
