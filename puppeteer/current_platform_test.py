#!/usr/bin/env python3
"""
当前平台深度测试 - 使用正确的端口配置
"""

import asyncio
import logging
import time
import json
from datetime import datetime
from playwright.async_api import async_playwright

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CurrentPlatformTester:
    def __init__(self):
        self.base_url = "http://localhost:5175"  # 更新为当前前端端口
        self.api_url = "http://localhost:8000"
        self.browser = None
        self.page = None
        self.test_results = {
            "test_overview": {},
            "authentication_test": {},
            "navigation_test": {},
            "backend_connectivity": {},
            "ui_functionality": {},
            "issues_found": [],
            "recommendations": []
        }
        
    async def setup_browser(self):
        """初始化浏览器"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=False)
        self.page = await self.browser.new_page()
        
        # 设置监听器
        self.page.on("console", self._handle_console)
        self.page.on("pageerror", self._handle_page_error)
        self.page.on("requestfailed", self._handle_request_failed)
        
        logger.info("🚀 浏览器初始化完成")

    def _handle_console(self, msg):
        if msg.type == "error":
            self.test_results["issues_found"].append({
                "type": "console_error",
                "message": msg.text,
                "timestamp": datetime.now().isoformat()
            })

    def _handle_page_error(self, error):
        self.test_results["issues_found"].append({
            "type": "page_error", 
            "message": str(error),
            "timestamp": datetime.now().isoformat()
        })

    def _handle_request_failed(self, request):
        self.test_results["issues_found"].append({
            "type": "request_failed",
            "url": request.url,
            "method": request.method,
            "timestamp": datetime.now().isoformat()
        })

    async def test_platform_access(self):
        """测试平台访问能力"""
        logger.info("🔍 测试平台基本访问...")
        
        try:
            # 访问主页
            start_time = time.time()
            await self.page.goto(self.base_url, timeout=10000)
            await self.page.wait_for_load_state('networkidle', timeout=10000)
            load_time = time.time() - start_time
            
            title = await self.page.title()
            url = self.page.url
            
            # 检查页面基本元素
            body = await self.page.query_selector('body')
            has_vue_app = await self.page.evaluate("() => window.Vue || document.querySelector('#app')")
            
            self.test_results["test_overview"] = {
                "title": title,
                "url": url,
                "load_time": round(load_time, 2),
                "has_body": body is not None,
                "has_vue_app": has_vue_app is not None,
                "accessible": True
            }
            
            logger.info(f"✅ 平台可访问 - 标题: {title}")
            logger.info(f"   - 加载时间: {load_time:.2f}秒")
            logger.info(f"   - Vue应用: {'已加载' if has_vue_app else '未检测到'}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 平台访问失败: {e}")
            self.test_results["test_overview"]["error"] = str(e)
            self.test_results["test_overview"]["accessible"] = False
            return False

    async def test_navigation_and_routing(self):
        """测试导航和路由功能"""
        logger.info("🔍 测试页面导航和路由...")
        
        try:
            # 测试主要路由
            routes_to_test = [
                "/",
                "/login", 
                "/register",
                "/dashboard",
                "/market",
                "/trading",
                "/strategy",
                "/risk"
            ]
            
            navigation_results = {}
            
            for route in routes_to_test:
                try:
                    await self.page.goto(f"{self.base_url}{route}", timeout=8000)
                    await asyncio.sleep(1)  # 等待页面稳定
                    
                    current_url = self.page.url
                    title = await self.page.title()
                    
                    # 检查页面是否正常加载
                    content = await self.page.query_selector('body')
                    has_content = content is not None
                    
                    # 检查是否有错误页面
                    error_indicators = await self.page.query_selector_all('.error, .not-found, [class*="error"], [class*="404"]')
                    has_errors = len(error_indicators) > 0
                    
                    navigation_results[route] = {
                        "accessible": True,
                        "final_url": current_url,
                        "title": title,
                        "has_content": has_content,
                        "has_errors": has_errors
                    }
                    
                    status = "✅ 正常" if has_content and not has_errors else "⚠️ 有问题"
                    logger.info(f"   - {route}: {status} (标题: {title})")
                    
                except Exception as e:
                    navigation_results[route] = {
                        "accessible": False,
                        "error": str(e)
                    }
                    logger.info(f"   - {route}: ❌ 无法访问 ({str(e)[:50]})")
            
            self.test_results["navigation_test"] = navigation_results
            
            # 统计导航成功率
            successful_routes = sum(1 for result in navigation_results.values() 
                                  if result.get("accessible") and result.get("has_content"))
            total_routes = len(routes_to_test)
            
            logger.info(f"✅ 导航测试完成: {successful_routes}/{total_routes} 页面可访问")
            
        except Exception as e:
            logger.error(f"❌ 导航测试失败: {e}")
            self.test_results["navigation_test"]["error"] = str(e)

    async def test_authentication_flow(self):
        """测试认证流程"""
        logger.info("🔍 测试用户认证流程...")
        
        try:
            # 访问登录页面
            await self.page.goto(f"{self.base_url}/login", timeout=8000)
            await self.page.wait_for_load_state('networkidle', timeout=5000)
            
            # 检查登录页面元素
            login_form = await self.page.query_selector('form')
            username_input = await self.page.query_selector('input[type="text"], input[placeholder*="用户名"], input[placeholder*="邮箱"]')
            password_input = await self.page.query_selector('input[type="password"]')
            login_buttons = await self.page.query_selector_all('button')
            
            # 查找各种登录按钮
            demo_btn = None
            normal_login_btn = None
            
            for btn in login_buttons:
                text = await btn.inner_text()
                if "演示" in text or "demo" in text.lower():
                    demo_btn = btn
                elif "登录" in text or "login" in text.lower():
                    normal_login_btn = btn
            
            auth_elements = {
                "login_form": login_form is not None,
                "username_input": username_input is not None,
                "password_input": password_input is not None,
                "demo_login_button": demo_btn is not None,
                "normal_login_button": normal_login_btn is not None,
                "total_buttons": len(login_buttons)
            }
            
            logger.info("✅ 登录页面元素检查:")
            for element, exists in auth_elements.items():
                if isinstance(exists, bool):
                    logger.info(f"   - {element}: {'存在' if exists else '缺失'}")
                else:
                    logger.info(f"   - {element}: {exists}")
            
            # 测试演示登录功能
            demo_login_success = False
            if demo_btn:
                try:
                    logger.info("   - 尝试演示登录...")
                    await demo_btn.click()
                    await asyncio.sleep(3)
                    
                    current_url = self.page.url
                    demo_login_success = current_url != f"{self.base_url}/login"
                    
                    if demo_login_success:
                        logger.info(f"   - ✅ 演示登录成功，跳转到: {current_url}")
                    else:
                        logger.info("   - ❌ 演示登录未发生跳转")
                        
                except Exception as e:
                    logger.info(f"   - ❌ 演示登录失败: {e}")
            
            self.test_results["authentication_test"] = {
                "elements": auth_elements,
                "demo_login_success": demo_login_success,
                "current_url": self.page.url
            }
            
        except Exception as e:
            logger.error(f"❌ 认证流程测试失败: {e}")
            self.test_results["authentication_test"]["error"] = str(e)

    async def test_backend_connectivity(self):
        """测试后端连接性"""
        logger.info("🔍 测试后端API连接性...")
        
        try:
            import aiohttp
            
            backend_tests = {}
            
            async with aiohttp.ClientSession() as session:
                # 基础健康检查
                endpoints_to_test = [
                    {"path": "/", "name": "health_check"},
                    {"path": "/docs", "name": "api_docs"},
                    {"path": "/api/v1/auth/register", "name": "register_api"},
                    {"path": "/api/v1/auth/login", "name": "login_api"},
                    {"path": "/api/v1/market/stocks", "name": "market_data_api"},
                    {"path": "/api/v1/trading/accounts", "name": "trading_api"}
                ]
                
                for endpoint in endpoints_to_test:
                    try:
                        async with session.get(f"{self.api_url}{endpoint['path']}", timeout=5) as resp:
                            backend_tests[endpoint['name']] = {
                                "status": "accessible",
                                "status_code": resp.status,
                                "response_time": "< 5s"
                            }
                            
                            status_icon = "✅" if resp.status < 500 else "❌"
                            logger.info(f"   - {endpoint['name']}: {status_icon} {resp.status}")
                            
                    except Exception as e:
                        backend_tests[endpoint['name']] = {
                            "status": "error",
                            "message": str(e)
                        }
                        logger.info(f"   - {endpoint['name']}: ❌ 连接失败")
            
            self.test_results["backend_connectivity"] = backend_tests
            
            # 统计后端服务状态
            accessible_count = sum(1 for test in backend_tests.values() 
                                 if test.get("status") == "accessible")
            total_count = len(backend_tests)
            logger.info(f"✅ 后端连接测试完成: {accessible_count}/{total_count} 端点可访问")
            
        except Exception as e:
            logger.error(f"❌ 后端连接测试失败: {e}")
            self.test_results["backend_connectivity"]["error"] = str(e)

    async def test_ui_functionality(self):
        """测试UI功能"""
        logger.info("🔍 测试UI交互功能...")
        
        try:
            # 回到主页测试UI元素
            await self.page.goto(self.base_url, timeout=8000)
            await asyncio.sleep(2)
            
            # 检查常见UI元素
            ui_elements = {}
            
            # 导航元素
            nav_elements = await self.page.query_selector_all('nav, .nav, .navbar, .navigation')
            ui_elements["navigation"] = len(nav_elements)
            
            # 按钮元素
            buttons = await self.page.query_selector_all('button')
            ui_elements["buttons"] = len(buttons)
            
            # 链接元素
            links = await self.page.query_selector_all('a')
            ui_elements["links"] = len(links)
            
            # 表单元素
            forms = await self.page.query_selector_all('form')
            ui_elements["forms"] = len(forms)
            
            # 输入框元素
            inputs = await self.page.query_selector_all('input, textarea, select')
            ui_elements["inputs"] = len(inputs)
            
            logger.info("✅ UI元素统计:")
            for element_type, count in ui_elements.items():
                logger.info(f"   - {element_type}: {count}个")
            
            # 测试几个按钮的点击功能
            clickable_tests = {}
            if len(buttons) > 0:
                # 测试前3个按钮
                for i, button in enumerate(buttons[:3]):
                    try:
                        button_text = await button.inner_text()
                        if button_text.strip():
                            # 记录点击前的URL
                            before_url = self.page.url
                            await button.click()
                            await asyncio.sleep(1)
                            after_url = self.page.url
                            
                            clickable_tests[f"button_{i+1}"] = {
                                "text": button_text,
                                "clickable": True,
                                "url_changed": before_url != after_url,
                                "before_url": before_url,
                                "after_url": after_url
                            }
                            
                            logger.info(f"   - 按钮{i+1} '{button_text}': ✅ 可点击")
                            
                    except Exception as e:
                        clickable_tests[f"button_{i+1}"] = {
                            "clickable": False,
                            "error": str(e)
                        }
            
            ui_elements["clickable_tests"] = clickable_tests
            self.test_results["ui_functionality"] = ui_elements
            
        except Exception as e:
            logger.error(f"❌ UI功能测试失败: {e}")
            self.test_results["ui_functionality"]["error"] = str(e)

    async def generate_comprehensive_report(self):
        """生成综合测试报告"""
        logger.info("📊 生成综合测试报告...")
        
        # 计算总体得分
        score_breakdown = {
            "platform_access": 0,
            "navigation": 0,
            "authentication": 0,
            "backend_connectivity": 0,
            "ui_functionality": 0
        }
        
        # 平台访问得分
        if self.test_results["test_overview"].get("accessible"):
            score_breakdown["platform_access"] = 20
            
        # 导航得分
        nav_results = self.test_results.get("navigation_test", {})
        if isinstance(nav_results, dict):
            successful_routes = sum(1 for result in nav_results.values() 
                                  if isinstance(result, dict) and result.get("accessible") 
                                  and result.get("has_content"))
            total_routes = len([k for k in nav_results.keys() if k != "error"])
            if total_routes > 0:
                score_breakdown["navigation"] = int(20 * successful_routes / total_routes)
        
        # 认证得分
        auth_results = self.test_results.get("authentication_test", {})
        if auth_results.get("demo_login_success"):
            score_breakdown["authentication"] = 20
        elif auth_results.get("elements", {}).get("demo_login_button"):
            score_breakdown["authentication"] = 10
            
        # 后端连接得分
        backend_results = self.test_results.get("backend_connectivity", {})
        if isinstance(backend_results, dict):
            accessible_apis = sum(1 for result in backend_results.values() 
                                if isinstance(result, dict) and result.get("status") == "accessible")
            total_apis = len([k for k in backend_results.keys() if k != "error"])
            if total_apis > 0:
                score_breakdown["backend_connectivity"] = int(20 * accessible_apis / total_apis)
        
        # UI功能得分
        ui_results = self.test_results.get("ui_functionality", {})
        if isinstance(ui_results, dict) and ui_results.get("buttons", 0) > 0:
            score_breakdown["ui_functionality"] = 20
        
        total_score = sum(score_breakdown.values())
        
        # 生成建议
        recommendations = []
        
        if score_breakdown["platform_access"] < 20:
            recommendations.append("修复平台基础访问问题")
            
        if score_breakdown["navigation"] < 15:
            recommendations.append("优化页面路由和导航功能")
            
        if score_breakdown["authentication"] < 15:
            recommendations.append("完善用户认证和登录流程")
            
        if score_breakdown["backend_connectivity"] < 15:
            recommendations.append("检查后端API服务状态")
            
        if score_breakdown["ui_functionality"] < 15:
            recommendations.append("增强UI交互功能")
            
        if len(self.test_results["issues_found"]) > 0:
            recommendations.append("修复前端JavaScript错误和网络请求问题")
            
        if not recommendations:
            recommendations.append("平台基础功能正常，建议进行更深入的功能测试")
        
        self.test_results["recommendations"] = recommendations
        
        # 输出测试报告
        logger.info("=" * 80)
        logger.info("📋 量化投资平台深度测试报告")
        logger.info("=" * 80)
        logger.info(f"🎯 总体得分: {total_score}/100")
        logger.info("")
        
        logger.info("📊 得分明细:")
        for category, score in score_breakdown.items():
            logger.info(f"   - {category}: {score}/20")
        logger.info("")
        
        logger.info("🏗️  平台基础状态:")
        overview = self.test_results["test_overview"]
        logger.info(f"   - 平台标题: {overview.get('title', 'N/A')}")
        logger.info(f"   - 加载时间: {overview.get('load_time', 0):.2f}秒")
        logger.info(f"   - Vue应用: {'✅ 检测到' if overview.get('has_vue_app') else '❌ 未检测到'}")
        logger.info("")
        
        logger.info("🧭 导航测试结果:")
        nav_results = self.test_results.get("navigation_test", {})
        if isinstance(nav_results, dict):
            for route, result in nav_results.items():
                if isinstance(result, dict):
                    status = "✅ 正常" if result.get("accessible") and result.get("has_content") else "❌ 异常"
                    logger.info(f"   - {route}: {status}")
        logger.info("")
        
        logger.info("🔐 认证系统状态:")
        auth = self.test_results.get("authentication_test", {})
        logger.info(f"   - 演示登录: {'✅ 功能正常' if auth.get('demo_login_success') else '❌ 需要修复'}")
        logger.info("")
        
        logger.info("🔧 后端连接状态:")
        backend = self.test_results.get("backend_connectivity", {})
        if isinstance(backend, dict):
            for service, result in backend.items():
                if isinstance(result, dict) and service != "error":
                    status = result.get("status", "unknown")
                    icon = "✅" if status == "accessible" else "❌"
                    logger.info(f"   - {service}: {icon} {status}")
        logger.info("")
        
        if self.test_results["issues_found"]:
            logger.info("⚠️  发现的问题:")
            for issue in self.test_results["issues_found"][:5]:
                logger.info(f"   - {issue['type']}: {issue.get('message', issue.get('url', 'N/A'))[:100]}")
            if len(self.test_results["issues_found"]) > 5:
                logger.info(f"   - ... 还有 {len(self.test_results['issues_found']) - 5} 个问题")
            logger.info("")
        
        logger.info("💡 改进建议:")
        for i, rec in enumerate(recommendations, 1):
            logger.info(f"   {i}. {rec}")
        logger.info("")
        
        # 总结
        if total_score >= 80:
            logger.info("🎉 平台功能完整，运行状态优秀！")
        elif total_score >= 60:
            logger.info("✅ 平台基础功能正常，还有改进空间")
        elif total_score >= 40:
            logger.info("⚠️ 平台部分功能正常，建议重点优化")
        else:
            logger.info("❌ 平台存在较多问题，需要全面修复")
        
        # 保存详细报告
        report_data = {
            "test_timestamp": datetime.now().isoformat(),
            "total_score": total_score,
            "score_breakdown": score_breakdown,
            "detailed_results": self.test_results,
            "recommendations": recommendations
        }
        
        report_file = f"platform_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📄 详细报告已保存: {report_file}")
        
        return report_data

    async def run_comprehensive_test(self):
        """运行综合测试"""
        logger.info("🚀 开始量化投资平台深度测试...")
        logger.info("=" * 80)
        
        try:
            await self.setup_browser()
            
            # 1. 测试平台基础访问
            platform_accessible = await self.test_platform_access()
            
            if platform_accessible:
                # 2. 测试导航和路由
                await self.test_navigation_and_routing()
                
                # 3. 测试认证流程
                await self.test_authentication_flow()
                
                # 4. 测试UI功能
                await self.test_ui_functionality()
            
            # 5. 测试后端连接（独立于前端）
            await self.test_backend_connectivity()
            
            # 6. 生成综合报告
            await self.generate_comprehensive_report()
            
        except Exception as e:
            logger.error(f"❌ 测试过程中发生严重错误: {e}")
        finally:
            if self.browser:
                await self.browser.close()
                logger.info("🔚 浏览器已关闭，测试完成")

async def main():
    tester = CurrentPlatformTester()
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())